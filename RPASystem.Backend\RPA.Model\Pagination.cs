﻿namespace RPA.Model
{
    /// <summary>
    /// 简单分页查询 
    /// </summary>
    /// <param name="pageIndex">当前页数</param>
    /// <param name="pageSize">每页显示大小</param>
    public record Pager(int pageIndex, int pageSize);

    /// <summary>
    /// 简单分页返回
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <param name="total">总记录数</param>
    /// <param name="items">数据列表</param>
    public record Paged<T>(int total, List<T> items);
}
