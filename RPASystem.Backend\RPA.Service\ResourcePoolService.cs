using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using RPASystem.Service;
using Infrastructure.Attribute;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IResourcePoolService), ServiceLifetime = LifeTime.Scoped)]
    public class ResourcePoolService : IResourcePoolService
    {
        private readonly RPASystemDbContext db;
        private readonly IResourceMachineService resourceMachineService;

        public ResourcePoolService(RPASystemDbContext context, IResourceMachineService resourceMachineService)
        {
            this.db = context;
            this.resourceMachineService = resourceMachineService;
        }

        /// <summary>
        /// 获取所有资源池列表
        /// </summary>
        public async Task<List<ResourcePool>> GetAllResourcePools()
        {
            return await db.ResourcePools.OrderByDescending(p => p.CreatedTime).ToListAsync();
        }

        /// <summary>
        /// 创建资源池
        /// </summary>
        public async Task<bool> CreateResourcePool(ResourcePool pool)
        {
            try
            {
                // 验证资源机名称是否都存在
                var machineNames = pool.MachineList;
                var existingMachines = await db.ResourceMachines
                    .Where(m => machineNames.Contains(m.MachineName))
                    .Select(m => m.MachineName)
                    .ToListAsync();

                if (existingMachines.Count != machineNames.Count)
                {
                    throw new Exception("存在无效的资源机名称");
                }
                pool.CreatedTime = DateTime.Now;
                await db.ResourcePools.AddAsync(pool);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 更新资源池
        /// </summary>
        public async Task<bool> UpdateResourcePool(ResourcePool pool)
        {
            try
            {
                var existingPool = await db.ResourcePools.FindAsync(pool.ID);
                if (existingPool == null) return false;

                // 验证资源机名称是否都存在
                var machineNames = pool.MachineList;
                var existingMachines = await db.ResourceMachines
                    .Where(m => machineNames.Contains(m.MachineName))
                    .Select(m => m.MachineName)
                    .ToListAsync();

                if (existingMachines.Count != machineNames.Count)
                {
                    throw new Exception("存在无效的资源机名称");
                }

                existingPool.PoolName = pool.PoolName;
                existingPool.Description = pool.Description;
                existingPool.ResourceMachineNames = pool.ResourceMachineNames;
                existingPool.UpdatedTime = DateTime.Now;

                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 删除资源池
        /// </summary>
        public async Task<bool> DeleteResourcePool(long id)
        {
            try
            {
                var pool = await db.ResourcePools.FindAsync(id);
                if (pool == null) return false;

                db.ResourcePools.Remove(pool);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }
    }
} 