## 实现方案
- 客户端录像模块：
使用开源的Screen Capture API（如SharpAvi或FFmpeg）集成到客户端
实用：FFmpeg
录像格式采用H.264编码的MP4，平衡文件大小和质量
录制区域: 全屏
- 录像触发机制：
在客户端接收到独享RPA任务时，检查任务的IsExclusive标志 且 JobModel 里 OutputFile 不为空（如果为空则表示不需要上传至服务器）
任务开始前启动录像，任务完成后停止录像

- 文件存储和命名规则：
本地临时存储路径：{客户端工作目录}\Recordings\{任务ID}_{时间戳}.mp4
最终上传路径与任务输出结果相同目录
- 上传机制：
任务完成后，通过现有文件上传机制上传录像文件
对于ServerIP参数存在的情况：将录像存放在\\{ServerIP}\VShare\WAutoDeploy\{程序名称}\OUTPUTRPASYSTEM\{JobTaskName}\{SubJobTaskName}\recording.mp4

- 前端查看功能：
无需前端查看功能

性能优化措施：
限制录像最大分辨率和帧率（如720p/10fps）
配置录像最大大小和自动分段机制（单文件大于1GB自动分段）
