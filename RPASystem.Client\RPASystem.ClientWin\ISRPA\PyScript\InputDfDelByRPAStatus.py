#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除DF中指定状态的数据

功能：删除DataFrame中"RPA状态"列值为"已完成"或"警告"的行
"""

import pandas as pd


def delete_df_by_rpa_status(df):
    """
    删除DF中指定状态的数据

    参数:
    - df: 输入的DataFrame，必须包含"RPA状态"列

    返回值:
    - DataFrame: 删除指定状态后的DataFrame

    异常:
    - ValueError: 如果DataFrame中不存在"RPA状态"列
    - TypeError: 如果输入参数不是DataFrame类型

    说明:
    - 删除"RPA状态"列中值为"已完成"或"警告"的行
    - 保留"RPA状态"列中值为"异常"或其他状态的行
    - 返回新的DataFrame，不修改原DataFrame
    """

    # 检查输入参数类型
    if not isinstance(df, pd.DataFrame):
        raise TypeError("输入参数必须是pandas DataFrame类型")

    # 检查是否存在"RPA状态"列
    if "RPA状态" not in df.columns:
        raise ValueError("DataFrame中不存在'RPA状态'列")

    # 创建DataFrame副本，避免修改原数据
    result_df = df.copy()

    # 删除"RPA状态"为"已完成"或"警告"的行
    # 使用~(取反)来保留不是"已完成"和"警告"的行
    condition = ~result_df["RPA状态"].isin(["已完成", "警告"])
    result_df = result_df[condition]

    # 重置索引
    result_df = result_df.reset_index(drop=True)

    print(f"原始数据行数: {len(df)}")
    print(f"删除后行数: {len(result_df)}")
    print(f"删除了 {len(df) - len(result_df)} 行数据")

    return result_df


def test_delete_df_by_rpa_status():
    """
    测试删除DF中指定状态数据的功能
    """
    print("=" * 50)
    print("开始测试删除DF中指定状态数据功能")
    print("=" * 50)

    # 测试用例1：正常情况
    print("\n测试用例1：正常情况")
    test_data1 = {
        "RPA状态": ["异常", "已完成", "警告", "异常", "已完成", "其他状态"],
        "编号": ["A001", "A002", "A003", "A004", "A005", "A006"],
        "名称": ["张三", "李四", "王五", "赵六", "魏七", "孙八"],
        "PRA信息": ["错误信息1", None, "警告信息1", "错误信息2", None, "其他信息"]
    }
    df1 = pd.DataFrame(test_data1)
    print("原始数据:")
    print(df1)

    result1 = delete_df_by_rpa_status(df1)
    print("\n删除后数据:")
    print(result1)

    # 验证结果
    expected_statuses = ["异常", "异常", "其他状态"]
    actual_statuses = result1["RPA状态"].tolist()
    assert actual_statuses == expected_statuses, f"期望状态: {expected_statuses}, 实际状态: {actual_statuses}"
    print("✓ 测试用例1通过")

    # 测试用例2：所有数据都需要删除
    print("\n测试用例2：所有数据都需要删除")
    test_data2 = {
        "RPA状态": ["已完成", "警告", "已完成"],
        "编号": ["B001", "B002", "B003"],
        "名称": ["用户1", "用户2", "用户3"]
    }
    df2 = pd.DataFrame(test_data2)
    print("原始数据:")
    print(df2)

    result2 = delete_df_by_rpa_status(df2)
    print("\n删除后数据:")
    print(result2)

    # 验证结果应该是空DataFrame
    assert len(result2) == 0, f"期望空DataFrame，实际行数: {len(result2)}"
    print("✓ 测试用例2通过")

    # 测试用例3：没有需要删除的数据
    print("\n测试用例3：没有需要删除的数据")
    test_data3 = {
        "RPA状态": ["异常", "异常", "其他状态"],
        "编号": ["C001", "C002", "C003"],
        "名称": ["用户A", "用户B", "用户C"]
    }
    df3 = pd.DataFrame(test_data3)
    print("原始数据:")
    print(df3)

    result3 = delete_df_by_rpa_status(df3)
    print("\n删除后数据:")
    print(result3)

    # 验证结果应该与原数据相同
    assert len(result3) == len(df3), f"期望行数: {len(df3)}, 实际行数: {len(result3)}"
    print("✓ 测试用例3通过")

    # 测试用例4：异常情况 - 缺少"RPA状态"列
    print("\n测试用例4：异常情况 - 缺少'RPA状态'列")
    test_data4 = {
        "编号": ["D001", "D002"],
        "名称": ["用户X", "用户Y"]
    }
    df4 = pd.DataFrame(test_data4)
    print("原始数据:")
    print(df4)

    try:
        result4 = delete_df_by_rpa_status(df4)
        print("❌ 测试用例4失败：应该抛出ValueError异常")
    except ValueError as e:
        print(f"✓ 测试用例4通过：正确抛出异常 - {e}")

    # 测试用例5：异常情况 - 输入非DataFrame类型
    print("\n测试用例5：异常情况 - 输入非DataFrame类型")
    try:
        result5 = delete_df_by_rpa_status("这不是DataFrame")
        print("❌ 测试用例5失败：应该抛出TypeError异常")
    except TypeError as e:
        print(f"✓ 测试用例5通过：正确抛出异常 - {e}")

    print("\n" + "=" * 50)
    print("所有测试用例通过！")
    print("=" * 50)


if __name__ == "__main__":
    # 运行测试
    test_delete_df_by_rpa_status()