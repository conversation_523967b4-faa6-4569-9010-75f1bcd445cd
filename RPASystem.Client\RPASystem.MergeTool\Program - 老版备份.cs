﻿using CommandLine;
using OfficeOpenXml;
using System.Collections.Generic;
using System.IO;
using System;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace RPASystem.MergeTool
{
    internal class Program
    {
        /// <summary>
        /// --t --targetPath: 要合并的目标文件夹
        /// --s --sourcePaths: 要合并的源文件夹, 可以有多个，用"|"分隔，支持连续数字循环文件夹地址，如：D:\A-{X}\Output，则表示D盘下的A-X文件夹下的Output文件夹，X为数字，每次递增1，直到找不到文件夹为止。
        /// --p --processType: 处理方式，用数字表示：
        ///     0: 不覆盖文件(如果目标文件有相同文件则重命名：在目标文件的基础加_序号，如AAA_1.EXE,AAA_2.EXE)，不合并Excel，Excel文件视为普通文件处理。
        ///     1: 覆盖文件，不合并Excel
        ///     2: 不覆盖文件(重命名：加_序号，如AAA_1.EXE)，合并Excel(合并条件是Sheet名相同并列名也相同，不同则重命名)
        ///     3: 覆盖文件，合并Excel(合并条件是Sheet名相同并列名也相同，不同则覆盖),或合并Csv
        ///     默认值：1
        /// </summary>
        /// <param name="args"></param>
        static void Main(string[] args)
        {
            try
            {
                Parser.Default.ParseArguments<Options>(args)
                    .WithParsed<Options>(RunOptions)
                    .WithNotParsed(HandleParseError);
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        private class Options
        {
            [Option('t', "targetPath", Required = true, HelpText = "要合并的目标文件夹")]
            public string TargetPath { get; set; }

            [Option('s', "sourcePaths", Required = true, HelpText = "要合并的源文件夹")]
            public string SourcePaths { get; set; }

            [Option('p', "processType", Required = false, Default = "1",
                HelpText = "处理方式：0-不覆盖不合并，1-覆盖不合并，2-不覆盖合并Excel，3-覆盖合并Excel")]
            public string ProcessType { get; set; }
        }

        private static void RunOptions(Options opts)
        {
            string sRet = string.Empty;
            try
            {
                // 检查并创建目标目录
                if (!Directory.Exists(opts.TargetPath))
                {
                    Directory.CreateDirectory(opts.TargetPath);
                }

                // 解析处理类型
                bool overwrite = opts.ProcessType == "1" || opts.ProcessType == "3";
                bool mergeExcel = opts.ProcessType == "2" || opts.ProcessType == "3";

                // 处理源路径
                var sourcePaths = ExpandSourcePaths(opts.SourcePaths);

                // 执行合并
                foreach (var sourcePath in sourcePaths)
                {
                    if (Directory.Exists(sourcePath))
                    {
                        ProcessDirectory(sourcePath, opts.TargetPath, overwrite, mergeExcel);
                    }
                }

                Console.WriteLine("合并完成！");
                sRet = "合并完成";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误：{ex.Message}");
                sRet = "合并异常：" + ex.Message;
            }

            //返回值
            var dir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ReturnVal");
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            var pathFull = Path.Combine(dir, Process.GetCurrentProcess().Id.ToString() + ".txt");
            File.WriteAllText(pathFull, sRet);
        }

        private static void HandleParseError(IEnumerable<Error> errs)
        {
            Console.WriteLine("参数错误，请检查参数！");
            foreach (var error in errs)
            {
                Console.WriteLine(error.ToString());
            }
        }

        private static List<string> ExpandSourcePaths(string sourcePaths)
        {
            var result = new List<string>();
            var paths = sourcePaths.Split('|');

            foreach (var path in paths)
            {
                if (path.Contains("{X}"))
                {
                    var basePattern = path.Replace("{X}", "");
                    var directory = Path.GetDirectoryName(basePattern);
                    int index = 1;

                    while (true)
                    {
                        string currentPath = path.Replace("{X}", index.ToString());
                        if (Directory.Exists(currentPath))
                        {
                            result.Add(currentPath);
                            index++;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                else
                {
                    result.Add(path);
                }
            }

            return result;
        }

        private static void ProcessDirectory(string sourcePath, string targetPath, bool overwrite, bool mergeExcel)
        {
            var dir = new DirectoryInfo(sourcePath);

            foreach (var file in dir.GetFiles())
            {
                var targetFilePath = Path.Combine(targetPath, file.Name);

                if ((file.Extension.ToLower() == ".xlsx" && mergeExcel) || 
                    (file.Extension.ToLower() == ".csv" && mergeExcel))
                {
                    if (file.Extension.ToLower() == ".csv")
                    {
                        MergeCsvFile(file.FullName, targetFilePath, overwrite);
                    }
                    else
                    {
                        MergeExcelFile(file.FullName, targetFilePath, overwrite);
                    }
                }
                else
                {
                    CopyFile(file.FullName, targetFilePath, overwrite);
                }
            }

            // 处理子目录
            foreach (var subDir in dir.GetDirectories())
            {
                string newTargetPath = Path.Combine(targetPath, subDir.Name);
                if (!Directory.Exists(newTargetPath))
                {
                    Directory.CreateDirectory(newTargetPath);
                }
                ProcessDirectory(subDir.FullName, newTargetPath, overwrite, mergeExcel);
            }
        }

        private static void CopyFile(string sourceFile, string targetFile, bool overwrite)
        {
            if (!overwrite && File.Exists(targetFile))
            {
                targetFile = GetUniqueFileName(targetFile);
            }
            File.Copy(sourceFile, targetFile, overwrite);
        }

        private static string GetUniqueFileName(string filePath)
        {
            string directory = Path.GetDirectoryName(filePath);
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string extension = Path.GetExtension(filePath);
            int index = 1;

            while (File.Exists(filePath))
            {
                filePath = Path.Combine(directory, $"{fileName}_{index}{extension}");
                index++;
            }

            return filePath;
        }

        private static void MergeExcelFile(string sourceFile, string targetFile, bool overwrite)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            if (!File.Exists(targetFile))
            {
                File.Copy(sourceFile, targetFile);
                return;
            }

            using (var sourcePackage = new ExcelPackage(new FileInfo(sourceFile)))
            using (var targetPackage = new ExcelPackage(new FileInfo(targetFile)))
            {
                foreach (var sourceSheet in sourcePackage.Workbook.Worksheets)
                {
                    var targetSheet = targetPackage.Workbook.Worksheets[sourceSheet.Name];

                    if (targetSheet == null)
                    {
                        targetPackage.Workbook.Worksheets.Add(sourceSheet.Name, sourceSheet);
                    }
                    else if (ValidateHeaders(sourceSheet, targetSheet))
                    {
                        int targetRow = targetSheet.Dimension.End.Row + 1;
                        int sourceRows = sourceSheet.Dimension.End.Row;
                        int cols = sourceSheet.Dimension.End.Column;

                        for (int row = 2; row <= sourceRows; row++)
                        {
                            for (int col = 1; col <= cols; col++)
                            {
                                targetSheet.Cells[targetRow, col].Value = sourceSheet.Cells[row, col].Value;
                            }
                            targetRow++;
                        }
                    }
                    else if (overwrite)
                    {
                        targetPackage.Workbook.Worksheets.Delete(targetSheet);
                        targetPackage.Workbook.Worksheets.Add(sourceSheet.Name, sourceSheet);
                    }
                }
                targetPackage.Save();
            }
        }

        private static bool ValidateHeaders(ExcelWorksheet sourceSheet, ExcelWorksheet targetSheet)
        {
            if (sourceSheet.Dimension == null || targetSheet.Dimension == null)
                return false;

            int sourceCols = sourceSheet.Dimension.End.Column;
            int targetCols = targetSheet.Dimension.End.Column;

            if (sourceCols != targetCols) return false;

            for (int col = 1; col <= sourceCols; col++)
            {
                if (sourceSheet.Cells[1, col].Text != targetSheet.Cells[1, col].Text)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 合并csv文件，如果表头相同，则合并数据，如果表头不同，则根据overwrite参数决定覆盖目标文件或创建新文件
        /// </summary>
        /// <param name="sourceFile">源文件</param>
        /// <param name="targetFile">目标文件</param>
        /// <param name="overwrite">是否覆盖</param>
        private static void MergeCsvFile(string sourceFile, string targetFile, bool overwrite)
        {
            // 如果目标文件不存在，直接复制
            if (!File.Exists(targetFile))
            {
                File.Copy(sourceFile, targetFile);
                return;
            }

            // 读取源文件和目标文件的所有行
            var sourceLines = File.ReadAllLines(sourceFile);
            var targetLines = File.ReadAllLines(targetFile);

            // 如果源文件为空，不做处理
            if (sourceLines.Length == 0) return;

            // 获取并比较表头
            var sourceHeader = sourceLines[0].Trim();
            var targetHeader = targetLines[0].Trim();

            if (sourceHeader == targetHeader)
            {
                // 表头相同，合并数据
                var mergedContent = new List<string>();
                // 添加表头
                mergedContent.Add(targetHeader);
                // 添加目标文件的数据行（跳过表头）
                for (int i = 1; i < targetLines.Length; i++)
                {
                    mergedContent.Add(targetLines[i]);
                }
                // 添加源文件的数据行（跳过表头）
                for (int i = 1; i < sourceLines.Length; i++)
                {
                    mergedContent.Add(sourceLines[i]);
                }

                // 获取源文件的编码
                var sourceEncoding = GetFileEncoding(sourceFile);
                
                // 使用相同的编码写入合并后的内容
                File.WriteAllLines(targetFile, mergedContent.ToArray(), sourceEncoding);
            }
            else
            {
                // 表头不同
                if (overwrite)
                {
                    // 覆盖模式：直接用源文件覆盖目标文件
                    File.Copy(sourceFile, targetFile, true);
                }
                else
                {
                    // 不覆盖模式：创建新文件
                    string newFile = GetUniqueFileName(targetFile);
                    File.Copy(sourceFile, newFile);
                }
            }
        }

        // 添加辅助方法来检测文件编码
        private static Encoding GetFileEncoding(string filename)
        {
            // 读取文件的前几个字节来检测 BOM
            var bom = new byte[4];
            using (var file = new FileStream(filename, FileMode.Open, FileAccess.Read))
            {
                file.Read(bom, 0, 4);
            }

            // 根据 BOM 判断编码
            if (bom[0] == 0xef && bom[1] == 0xbb && bom[2] == 0xbf) return Encoding.UTF8;
            if (bom[0] == 0xff && bom[1] == 0xfe) return Encoding.Unicode; //UTF-16LE
            if (bom[0] == 0xfe && bom[1] == 0xff) return Encoding.BigEndianUnicode; //UTF-16BE
            if (bom[0] == 0 && bom[1] == 0 && bom[2] == 0xfe && bom[3] == 0xff) return Encoding.UTF32;

            // 如果没有 BOM，默认使用系统默认编码（通常是 GB2312 或 GBK）
            return Encoding.Default;
        }
    }
}
