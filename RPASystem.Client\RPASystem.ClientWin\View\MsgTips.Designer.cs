﻿namespace RPASystem.Client.View
{
    partial class MsgTips
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblMsg = new Sunny.UI.UILabel();
            this.linkUrl = new Sunny.UI.UILinkLabel();
            this.SuspendLayout();
            // 
            // lblMsg
            // 
            this.lblMsg.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMsg.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblMsg.Location = new System.Drawing.Point(18, 45);
            this.lblMsg.Name = "lblMsg";
            this.lblMsg.Size = new System.Drawing.Size(230, 95);
            this.lblMsg.TabIndex = 0;
            this.lblMsg.Text = "消息内容显示！！！\r\n支持换行。。。。";
            // 
            // linkUrl
            // 
            this.linkUrl.ActiveLinkColor = System.Drawing.Color.Blue;
            this.linkUrl.Font = new System.Drawing.Font("Arial", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.linkUrl.ForeColor = System.Drawing.Color.DodgerBlue;
            this.linkUrl.LinkBehavior = System.Windows.Forms.LinkBehavior.AlwaysUnderline;
            this.linkUrl.Location = new System.Drawing.Point(90, 168);
            this.linkUrl.Name = "linkUrl";
            this.linkUrl.Size = new System.Drawing.Size(82, 23);
            this.linkUrl.TabIndex = 1;
            this.linkUrl.TabStop = true;
            this.linkUrl.Text = "查看详情";
            this.linkUrl.VisitedLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            // 
            // MsgTips
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(267, 206);
            this.Controls.Add(this.linkUrl);
            this.Controls.Add(this.lblMsg);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "MsgTips";
            this.ShowIcon = false;
            this.Text = "消息提醒";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 450);
            this.Load += new System.EventHandler(this.MsgTips_Load);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UILabel lblMsg;
        private Sunny.UI.UILinkLabel linkUrl;
    }
}