﻿using System;
using System.IO;


public static class RPACaseUtils
{
    /// <summary>
    /// 异常excel文件名
    /// </summary>
    public const string ExceptionLogFilename = "ExceptionsLog.xlsx";

    private const string BaseDirectory = @"D:\isearch\RPACase";



    public static string GetPath(string rpaName, RPAFolder folder)
    {
        string path = Path.Combine(BaseDirectory, rpaName);

        if (folder != RPAFolder.Root)
        {
            path = Path.Combine(path, folder.ToString());
        }

        return path;
    }
}

public enum RPAFolder
{
    Root,
    Application,
    Input,
    Config,
    Log,
    Output
}