﻿using OfficeOpenXml;
using System.Collections.Generic;
using System.IO;
using System;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Windows.Input;

namespace RPASystem.MergeTool
{
    internal class Program
    {
        /// <summary>
        /// --t --targetPath: 要合并的目标文件夹
        /// --s --sourcePaths: 要合并的源文件夹, 可以有多个，用"|"分隔，支持连续数字循环文件夹地址，如：D:\A-{X}\Output，则表示D盘下的A-X文件夹下的Output文件夹，X为数字，每次递增1，直到找不到文件夹为止。
        /// --p --processType: 处理方式，用数字表示：
        ///     0: 不覆盖文件(如果目标文件有相同文件则重命名：在目标文件的基础加_序号，如AAA_1.EXE,AAA_2.EXE)，不合并Excel，Excel文件视为普通文件处理。
        ///     1: 覆盖文件，不合并Excel
        ///     2: 不覆盖文件(重命名：加_序号，如AAA_1.EXE)，合并Excel(合并条件是Sheet名相同并列名也相同，不同则重命名)
        ///     3: 覆盖文件，合并Excel(合并条件是Sheet名相同并列名也相同，不同则覆盖),或合并Csv
        ///     默认值：1
        /// </summary>
        /// <param name="args"></param>
        static void Main(string[] args)
        {
            //CheckFileBusiness=主要的业务逻辑类，InputModel=自定义的输入参数Model
            MainFrameProxy.RunEntryRepository<VGBusiness, Options>(args);

        }

        //private class Options
        //{
        //    [Option('t', "targetPath", Required = true, HelpText = "要合并的目标文件夹")]
        //    public string TargetPath { get; set; }

        //    [Option('s', "sourcePaths", Required = true, HelpText = "要合并的源文件夹")]
        //    public string SourcePaths { get; set; }

        //    [Option('p', "processType", Required = false, Default = "1",
        //        HelpText = "处理方式：0-不覆盖不合并，1-覆盖不合并，2-不覆盖合并Excel，3-覆盖合并Excel")]
        //    public string ProcessType { get; set; }
        //}
    }
}
