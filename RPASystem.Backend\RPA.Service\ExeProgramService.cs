﻿using RPASystem.Model;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using RPASystem.Service;
using Infrastructure.Attribute;
using System.IO;
using Microsoft.AspNetCore.Http;
using System.IO.Compression;
using Microsoft.Extensions.Configuration;
using RPASystem.Service.Common;
using RPA.Model;

namespace RPASystem.Service
{

    [AppService(ServiceType = typeof(IExeProgramService), ServiceLifetime = LifeTime.Scoped)]
    public class ExeProgramService : IExeProgramService
    {
        private readonly RPASystemDbContext dbContext;
        private const string ProgramPackageType = "ProgramPackage";

        public ExeProgramService(RPASystemDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public IEnumerable<object> GetAllExeProgramsForList()
        {
            // 只查询前端需要的字段
            return dbContext.ExePrograms
                .Select(p => new
                {
                    id = p.ID,                    // 注意：前端用的是小写的id
                    programName = p.ProgramName,
                    inputParameters = p.InputParameters,
                    programType = p.ProgramType,
                    resourceSelection = p.ResourceSelection,
                    notificationResourceMachines = p.NotificationResourceMachines,
                    version = p.Version,
                    isExclusive = p.IsExclusive,
                    remarks = p.Remarks,
                    createdAt = p.CreatedAt,
                    updatedAt = p.UpdatedAt
                })
                .ToList();
        }

        public ExeProgram GetExeProgramById(long id)
        {
            return dbContext.ExePrograms.FirstOrDefault(p => p.ID == id);
        }

        private string GetNextVersion(string currentVersion)
        {
            if (string.IsNullOrEmpty(currentVersion))
            {
                return "1.0.0.1";
            }

            var versionParts = currentVersion.Split('.').Select(int.Parse).ToArray();
            if (versionParts.Length != 4)
            {
                return "1.0.0.1";
            }

            // 增加最后一位，如果超过9则进位
            versionParts[3]++;
            if (versionParts[3] > 9)
            {
                versionParts[3] = 0;
                versionParts[2]++;
                if (versionParts[2] > 9)
                {
                    versionParts[2] = 0;
                    versionParts[1]++;
                    if (versionParts[1] > 9)
                    {
                        versionParts[1] = 0;
                        versionParts[0]++;
                    }
                }
            }

            return string.Join(".", versionParts);
        }

        private string? GetVersionFromZipFile(IFormFile zipFile)
        {
            return null;
            try
            {
                using (var stream = zipFile.OpenReadStream())
                using (var archive = new ZipArchive(stream, ZipArchiveMode.Read))
                {
                    // 查找Version.dat文件
                    var versionEntry = archive.GetEntry("Version.dat");
                    if (versionEntry != null)
                    {
                        using (var reader = new StreamReader(versionEntry.Open()))
                        {
                            var version = reader.ReadToEnd().Trim();
                            // 验证版本号格式是否正确 (x.x.x.x)
                            if (Regex.IsMatch(version, @"^\d+\.\d+\.\d+\.\d+$"))
                            {
                                return version;
                            }
                        }
                    }
                }
            }
            catch
            {
                // 如果读取失败，返回null使用默认逻辑
            }
            return null;
        }

        /// <summary>
        /// 保存EXE程序，支持多种文件上传方式
        /// </summary>
        /// <param name="exeProgram">程序对象</param>
        /// <param name="programPackageFile">小文件上传时的IFormFile对象</param>
        /// <param name="tempFilePath">大文件上传时的临时文件路径</param>
        public void SaveExeProgram(ExeProgram exeProgram, IFormFile? programPackageFile = null, string? tempFilePath = null)
        {
            bool hasNewFile = programPackageFile != null || !string.IsNullOrEmpty(tempFilePath);
            bool isUpdate = exeProgram.ID > 0;
            string fileName = $"{exeProgram.ProgramName}.zip";
            
            // 1. 处理数据库记录
            if (isUpdate)
            {
                // 更新现有记录
                var existingProgram = dbContext.ExePrograms.AsNoTracking().FirstOrDefault(p => p.ID == exeProgram.ID);
                if (existingProgram == null)
                {
                    throw new Exception($"未找到ID为{exeProgram.ID}的程序");
                }
                
                // 处理版本号
                if (hasNewFile)
                {
                    // 更新时使用原有的版本号递增逻辑
                    try
                    {
                        var currentVersion = new Version(existingProgram.Version);
                        exeProgram.Version = new Version(currentVersion.Major, currentVersion.Minor, currentVersion.Build, currentVersion.Revision + 1).ToString();
                    }
                    catch
                    {
                        // 如果原有版本号格式不正确，使用默认版本号
                        exeProgram.Version = "1.0.0.1";
                    }
                }
                else
                {
                    // 如果没有上传新的程序包，保持原版本号和文件路径
                    exeProgram.Version = existingProgram.Version;
                    exeProgram.ProgramPackage = existingProgram.ProgramPackage;
                }
                
                exeProgram.UpdatedAt = DateTime.Now;
                dbContext.ExePrograms.Update(exeProgram);
            }
            else
            {
                // 新增记录
                // 检查程序名唯一性
                var existingProgramWithSameName = dbContext.ExePrograms.FirstOrDefault(p => p.ProgramName == exeProgram.ProgramName);
                if (existingProgramWithSameName != null)
                {
                    throw new Exception($"程序名'{exeProgram.ProgramName}'已存在，请使用其他名称");
                }
                
                // 新增程序时始终使用默认版本号1.0.0.1
                exeProgram.Version = "1.0.0.1";
                
                exeProgram.CreatedAt = DateTime.Now;
                exeProgram.UpdatedAt = DateTime.Now;
                dbContext.ExePrograms.Add(exeProgram);
            }
            
            // 2. 处理文件（如果有）
            if (hasNewFile)
            {
                // 准备目标文件路径
                string targetFilePath = FileUploadManager.GetFileRealPath(ProgramPackageType, fileName);
                
                // 如果目标文件已存在，先删除
                if (File.Exists(targetFilePath))
                {
                    File.Delete(targetFilePath);
                }
                
                // 确保目标目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(targetFilePath));
                
                // 处理文件 - 根据来源选择不同处理方式
                if (programPackageFile != null)
                {
                    // 小文件：直接从IFormFile复制到目标位置
                    using (var fileStream = new FileStream(targetFilePath, FileMode.Create))
                    {
                        programPackageFile.CopyTo(fileStream);
                    }
                }
                else if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        // 大文件：移动临时文件到目标位置
                        // 如果源和目标在不同驱动器上，会自动执行复制+删除
                        File.Move(tempFilePath, targetFilePath, true);
                    }
                    catch (IOException)
                    {
                        // 如果移动失败（例如跨卷移动），则尝试复制
                        File.Copy(tempFilePath, targetFilePath, true);
                        
                        // 然后删除临时文件
                        try
                        {
                            File.Delete(tempFilePath);
                        }
                        catch
                        {
                            // 忽略删除临时文件时的错误
                        }
                    }
                }
                
                // 更新数据库中存储的路径（只存储文件名）
                exeProgram.ProgramPackage = fileName;
            }
            
            // 3. 保存所有更改到数据库
            dbContext.SaveChanges();
        }

        public void DeleteExeProgram(long id)
        {
            var exeProgram = dbContext.ExePrograms.FirstOrDefault(p => p.ID == id);
            if (exeProgram != null)
            {
                // 删除文件
                if (!string.IsNullOrEmpty(exeProgram.ProgramPackage))
                {
                    string absoluteFilePath = FileUploadManager.GetFileRealPath(ProgramPackageType, exeProgram.ProgramPackage);
                    if (File.Exists(absoluteFilePath))
                    {
                        File.Delete(absoluteFilePath);
                    }
                }

                dbContext.ExePrograms.Remove(exeProgram);
                dbContext.SaveChanges();
            }
        }

        public IEnumerable<object> SearchExePrograms(string query)
        {
            var queryLower = query?.ToLower() ?? "";
            return dbContext.ExePrograms
                .Where(p => string.IsNullOrEmpty(query) || p.ProgramName.ToLower().Contains(queryLower))
                .Select(p => new
                {
                    id = p.ID,
                    programName = p.ProgramName,
                    inputParameters = p.InputParameters,
                    programType = p.ProgramType,
                    isExclusive = p.IsExclusive,
                    programPackage = p.ProgramPackage,
                    version = p.Version,
                    createdAt = p.CreatedAt,
                    updatedAt = p.UpdatedAt,
                    remarks = p.Remarks,
                    resourceSelection = p.ResourceSelection,
                    notificationResourceMachines = p.NotificationResourceMachines
                })
                .ToList();
        }

        public Paged<object> SearchExeProgramsWithPaging(string query, Pager pager)
        {
            var queryLower = query?.ToLower() ?? "";

            // 构建查询
            var queryable = dbContext.ExePrograms
                .Where(p => string.IsNullOrEmpty(query) || p.ProgramName.ToLower().Contains(queryLower));

            // 获取总数
            var totalCount = queryable.Count();

            // 应用分页并获取数据
            var pagedData = queryable
                .OrderByDescending(p => p.CreatedAt) // 按创建时间倒序排列
                .Skip((pager.pageIndex - 1) * pager.pageSize)
                .Take(pager.pageSize)
                .Select(p => new
                {
                    id = p.ID,
                    programName = p.ProgramName,
                    inputParameters = p.InputParameters,
                    programType = p.ProgramType,
                    isExclusive = p.IsExclusive,
                    programPackage = p.ProgramPackage,
                    version = p.Version,
                    createdAt = p.CreatedAt,
                    updatedAt = p.UpdatedAt,
                    remarks = p.Remarks,
                    resourceSelection = p.ResourceSelection,
                    notificationResourceMachines = p.NotificationResourceMachines
                })
                .ToList();

            // 返回分页结果
            return new Paged<object>(totalCount, pagedData.Cast<object>().ToList());
        }

        public ExeProgram GetExeProgramByName(string programName)
        {
            return dbContext.ExePrograms.FirstOrDefault(p => p.ProgramName == programName);
        }

        /// <summary>
        /// 获取所有资源（资源池和资源机）
        /// </summary>
        public object GetAllResources()
        {
            var resourcePools = dbContext.ResourcePools
                .Select(p => new
                {
                    id = p.ID,
                    poolName = p.PoolName,
                    isPool = true
                })
                .ToList();

            var resourceMachines = dbContext.ResourceMachines
                .Select(m => new
                {
                    id = m.Id,
                    machineName = m.MachineName,
                    isPool = false
                })
                .ToList();

            return new
            {
                resourcePools = resourcePools,
                resourceMachines = resourceMachines
            };


        }

        public byte[] DownloadExeProgram(string programName)
        {
            var exeProgram = dbContext.ExePrograms.FirstOrDefault(p => p.ProgramName == programName);
            if (exeProgram != null && !string.IsNullOrEmpty(exeProgram.ProgramPackage))
            {
                string absoluteFilePath = FileUploadManager.GetFileRealPath(ProgramPackageType, exeProgram.ProgramPackage);
                if (File.Exists(absoluteFilePath))
                {
                    return File.ReadAllBytes(absoluteFilePath);
                }
            }
            return null;
        }
    }
}
