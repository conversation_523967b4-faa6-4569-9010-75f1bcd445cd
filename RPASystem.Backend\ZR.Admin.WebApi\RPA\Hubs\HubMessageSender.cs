using Microsoft.AspNetCore.SignalR;
using RPASystem.WebApi.Hubs;
using Microsoft.Extensions.DependencyInjection;

namespace RPASystem.WebApi.Hubs
{
    /// <summary>
    /// 前端消息发送类
    /// 用于管理向前端发送消息的静态方法
    /// 简化消息管理，避免在每个Controller中注入IHubContext对象
    /// </summary>
    public static class HubMessageSender
    {
        private static IHubContext<ResourceMachineHub> hubContext;

        // 资源机刷新状态跟踪
        private static bool resourceMachineRefreshPending = false;
        private static bool resourceMachineRefreshInProgress = false;
        private static DateTime resourceMachineLastRefreshTime = DateTime.MinValue;

        // 任务刷新状态跟踪
        private static bool jobTaskRefreshPending = false;
        private static bool jobTaskRefreshInProgress = false;
        private static DateTime jobTaskLastRefreshTime = DateTime.MinValue;

        // 防抖时间窗口(毫秒)
        private const int REFRESH_WINDOW_MS = 500;

        /// <summary>
        /// 初始化HubContext
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            hubContext = serviceProvider.GetRequiredService<IHubContext<ResourceMachineHub>>();
        }

        /// <summary>
        /// 刷新资源机列表
        /// 5秒内只刷新一次，合并多个请求
        /// </summary>
        public static async Task RefreshResourceMachines()
        {
            // 判断是否在刷新窗口期内
            bool shouldRefreshNow = false;
            bool shouldScheduleRefresh = false;

            lock (typeof(HubMessageSender))
            {
                TimeSpan timeSinceLastRefresh = DateTime.Now - resourceMachineLastRefreshTime;
                
                // 如果是首次刷新或已经超过窗口期，立即刷新
                if (timeSinceLastRefresh.TotalMilliseconds >= REFRESH_WINDOW_MS || resourceMachineLastRefreshTime == DateTime.MinValue)
                {
                    if (!resourceMachineRefreshInProgress)
                    {
                        shouldRefreshNow = true;
                        resourceMachineRefreshInProgress = true;
                        resourceMachineRefreshPending = false;
                        resourceMachineLastRefreshTime = DateTime.Now;
                    }
                    else
                    {
                        // 如果已经有刷新在进行中，标记为待处理
                        resourceMachineRefreshPending = true;
                    }
                }
                else
                {
                    // 在窗口期内，如果没有刷新任务在进行中且没有调度刷新任务，则调度一个延迟刷新
                    if (!resourceMachineRefreshPending && !resourceMachineRefreshInProgress)
                    {
                        resourceMachineRefreshPending = true;
                        shouldScheduleRefresh = true;
                    }
                    else
                    {
                        // 已有待处理的刷新，不需要再次调度
                        resourceMachineRefreshPending = true;
                    }
                }
            }

            // 立即执行刷新
            if (shouldRefreshNow)
            {
                try
                {
                    // 实际发送刷新消息
                    if (hubContext != null)
                    {
                        await hubContext.Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
                    }
                }
                finally
                {
                    lock (typeof(HubMessageSender))
                    {
                        resourceMachineRefreshInProgress = false;
                    }
                }
            }
            // 调度延迟刷新
            else if (shouldScheduleRefresh)
            {
                // 计算需要等待的时间
                TimeSpan timeSinceLastRefresh = DateTime.Now - resourceMachineLastRefreshTime;
                int delayMs = REFRESH_WINDOW_MS - (int)timeSinceLastRefresh.TotalMilliseconds;
                
                _ = Task.Run(async () =>
                {
                    await Task.Delay(delayMs);
                    await PerformDelayedResourceMachineRefresh();
                });
            }
        }

        private static async Task PerformDelayedResourceMachineRefresh()
        {
            bool shouldRefresh = false;
            
            lock (typeof(HubMessageSender))
            {
                if (resourceMachineRefreshPending && !resourceMachineRefreshInProgress)
                {
                    resourceMachineRefreshPending = false;
                    resourceMachineRefreshInProgress = true;
                    resourceMachineLastRefreshTime = DateTime.Now;
                    shouldRefresh = true;
                }
            }
            
            if (shouldRefresh)
            {
                try
                {
                    if (hubContext != null)
                    {
                        await hubContext.Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
                    }
                }
                finally
                {
                    lock (typeof(HubMessageSender))
                    {
                        resourceMachineRefreshInProgress = false;
                    }
                }
            }
        }

        /// <summary>
        /// 刷新任务列表
        /// 5秒内只刷新一次，合并多个请求
        /// </summary>
        public static async Task RefreshJobTasks()
        {
            // 判断是否在刷新窗口期内
            bool shouldRefreshNow = false;
            bool shouldScheduleRefresh = false;

            lock (typeof(HubMessageSender))
            {
                TimeSpan timeSinceLastRefresh = DateTime.Now - jobTaskLastRefreshTime;
                
                // 如果是首次刷新或已经超过窗口期，立即刷新
                if (timeSinceLastRefresh.TotalMilliseconds >= REFRESH_WINDOW_MS || jobTaskLastRefreshTime == DateTime.MinValue)
                {
                    if (!jobTaskRefreshInProgress)
                    {
                        shouldRefreshNow = true;
                        jobTaskRefreshInProgress = true;
                        jobTaskRefreshPending = false;
                        jobTaskLastRefreshTime = DateTime.Now;
                    }
                    else
                    {
                        // 如果已经有刷新在进行中，标记为待处理
                        jobTaskRefreshPending = true;
                    }
                }
                else
                {
                    // 在窗口期内，如果没有刷新任务在进行中且没有调度刷新任务，则调度一个延迟刷新
                    if (!jobTaskRefreshPending && !jobTaskRefreshInProgress)
                    {
                        jobTaskRefreshPending = true;
                        shouldScheduleRefresh = true;
                    }
                    else
                    {
                        // 已有待处理的刷新，不需要再次调度
                        jobTaskRefreshPending = true;
                    }
                }
            }

            // 立即执行刷新
            if (shouldRefreshNow)
            {
                try
                {
                    // 实际发送刷新消息
                    if (hubContext != null)
                    {
                        await hubContext.Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                    }
                }
                finally
                {
                    lock (typeof(HubMessageSender))
                    {
                        jobTaskRefreshInProgress = false;
                    }
                }
            }
            // 调度延迟刷新
            else if (shouldScheduleRefresh)
            {
                // 计算需要等待的时间
                TimeSpan timeSinceLastRefresh = DateTime.Now - jobTaskLastRefreshTime;
                int delayMs = REFRESH_WINDOW_MS - (int)timeSinceLastRefresh.TotalMilliseconds;
                
                _ = Task.Run(async () =>
                {
                    await Task.Delay(delayMs);
                    await PerformDelayedJobTaskRefresh();
                });
            }
        }

        private static async Task PerformDelayedJobTaskRefresh()
        {
            bool shouldRefresh = false;
            
            lock (typeof(HubMessageSender))
            {
                if (jobTaskRefreshPending && !jobTaskRefreshInProgress)
                {
                    jobTaskRefreshPending = false;
                    jobTaskRefreshInProgress = true;
                    jobTaskLastRefreshTime = DateTime.Now;
                    shouldRefresh = true;
                }
            }
            
            if (shouldRefresh)
            {
                try
                {
                    if (hubContext != null)
                    {
                        await hubContext.Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                    }
                }
                finally
                {
                    lock (typeof(HubMessageSender))
                    {
                        jobTaskRefreshInProgress = false;
                    }
                }
            }
        }

        /// <summary>
        /// 向指定资源机发送消息
        /// </summary>
        /// <param name="machineName">资源机名称</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="args">参数</param>
        public static async Task SendToMachine(string machineName, string methodName, params object[] args)
        {
            if (hubContext != null && !string.IsNullOrEmpty(machineName))
            {
                string connectionId = ResourceMachineHub.machineConnections.GetValueOrDefault(machineName);
                if (!string.IsNullOrEmpty(connectionId))
                {
                    await hubContext.Clients.Client(connectionId).SendAsync(methodName, args);
                }
            }
        }

        /// <summary>
        /// 向所有Web客户端发送消息
        /// </summary>
        /// <param name="methodName">方法名称</param>
        /// <param name="args">参数</param>
        public static async Task SendToWebClients(string methodName, params object[] args)
        {
            if (hubContext != null)
            {
                await hubContext.Clients.Group("WebClients").SendAsync(methodName, args);
            }
        }
    }
} 