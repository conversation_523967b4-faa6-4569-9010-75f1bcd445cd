using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using RPASystem.Model;


public class RPASystemDbContext : DbContext
{

    public RPASystemDbContext(DbContextOptions<RPASystemDbContext> options) : base(options)
    {
    }


    public DbSet<ResourceMachine> ResourceMachines { get; set; }
    public DbSet<TestTable> TestTable { get; set; }
    public DbSet<ExeProgram> ExePrograms { get; set; }
    public DbSet<JobTask> JobTasks { get; set; }
    public DbSet<FileStorage> FileStorages { get; set; }
    // Add more DbSet properties for other entities as needed
    public DbSet<ResourcePool> ResourcePools { get; set; }
    public DbSet<RpaCredential> RpaCredentials { get; set; }
    public DbSet<SystemConfig> SystemConfigs { get; set; }
    

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        modelBuilder.Entity<RpaCredential>(entity =>
        {
            entity.ToTable("RpaCredentials");
            entity.HasKey(e => e.ID);
            entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Password).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(500);
        });

        modelBuilder.Entity<SystemConfig>(entity =>
        {
            entity.ToTable("SystemConfig");
            entity.HasKey(e => e.ID);
            entity.Property(e => e.ConfigKey).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ConfigValue).IsRequired().HasMaxLength(500);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.ConfigGroup).HasMaxLength(50);
            entity.HasIndex(e => e.ConfigKey).IsUnique();
        });
    }

}

