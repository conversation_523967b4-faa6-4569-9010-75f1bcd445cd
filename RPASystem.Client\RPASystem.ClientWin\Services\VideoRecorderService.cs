using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Drawing;
using System.Windows.Forms;
using NLog;
using System.Collections.Generic;

namespace RPASystem.ClientWin.Services
{
    /// <summary>
    /// 视频录制服务 - 使用FFmpeg进行屏幕录像
    /// </summary>
    public class VideoRecorderService : IDisposable
    {
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();
        private Process ffmpegProcess;
        private CancellationTokenSource cancellationTokenSource;
        private string outputDirectory;
        private bool isRecording;
        private int currentFileIndex = 0; // 分段索引，从0开始
        private string baseTimestamp; // 统一的时间戳，用于所有分段文件名
        private string currentOutputPath;

        // 录像配置参数
        public int FrameRate { get; set; } = 1;
        public int Resolution { get; set; } = 480; // 720p
        public long MaxFileSizeBytes { get; set; } = 1024 * 1024 * 1024; // 1GB

        // 单例模式实现
        private static readonly Lazy<VideoRecorderService> _instance = new Lazy<VideoRecorderService>(() => new VideoRecorderService());

        public static VideoRecorderService Instance => _instance.Value;

        // 私有构造函数，防止外部实例化
        private VideoRecorderService()
        {
        }

        /// <summary>
        /// 开始录像
        /// </summary>
        /// <param name="saveDirectory">指定保存目录</param>
        /// <returns>是否成功启动录像</returns>
        public bool StartRecording(string saveDirectory)
        {
            if (isRecording)
            {
                logger.Warn("尝试开始录像，但已经在录制中");
                return false;
            }

            try
            {
                // 检查保存目录
                if (string.IsNullOrEmpty(saveDirectory))
                {
                    logger.Error("保存目录不能为空");
                    return false;
                }

                // 确保保存目录存在
                if (!Directory.Exists(saveDirectory))
                {
                    Directory.CreateDirectory(saveDirectory);
                }

                outputDirectory = saveDirectory;
                isRecording = true;
                cancellationTokenSource = new CancellationTokenSource();
                currentFileIndex = 0;
                baseTimestamp = DateTime.Now.ToString("yyyy-MM-dd-HHmmss");

                return StartNewRecordingSegment();
            }
            catch (Exception ex)
            {
                isRecording = false;
                logger.Error(ex, "开始录像时出错");
                return false;
            }
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        /// <returns>成功返回true，失败返回false</returns>
        public void StopRecording()
        {
            //if (!isRecording || ffmpegProcess == null)
            //{
            //    logger.Warn("尝试停止录像，但没有正在进行的录制");
            //}

            while (isRecording || ffmpegProcess != null)
            {


                try
                {
                    // 取消文件大小监控
                    cancellationTokenSource?.Cancel();

                    // 优雅地结束FFmpeg进程
                    while (ffmpegProcess !=null && !ffmpegProcess.HasExited)
                    {
                        try
                        {
                            // 发送q键给FFmpeg以优雅地结束录制
                            ffmpegProcess.StandardInput.Write('q');
                            ffmpegProcess.StandardInput.Flush();

                            // 等待FFmpeg退出，最多等待5秒
                            if (!ffmpegProcess.WaitForExit(10000))
                            {
                                // 如果超时，则强制结束进程
                                ffmpegProcess.Kill();
                                logger.Warn("FFmpeg进程未响应，已强制终止");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error(ex, "优雅关闭FFmpeg失败，尝试强制关闭");
                            try
                            {
                                ffmpegProcess.Kill();
                            }
                            catch (Exception killEx)
                            {
                                logger.Error(killEx, "强制关闭FFmpeg失败");
                            }
                        }
                    }

                    ffmpegProcess?.Dispose();
                    ffmpegProcess = null;
                    isRecording = false;

                    logger.Info($"停止录制视频: {currentOutputPath}");
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "停止录像时出错");
                }
            }
        }

        /// <summary>
        /// 开始新的录像分段
        /// </summary>
        private bool StartNewRecordingSegment()
        {
            try
            {
                // 检查FFmpeg是否存在
                string ffmpegPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ffmpeg.exe");
                if (!File.Exists(ffmpegPath))
                {
                    logger.Error($"找不到FFmpeg: {ffmpegPath}");
                    return false;
                }

                // 根据分段索引确定文件名
                string fileName = currentFileIndex == 0
                    ? $"录像{baseTimestamp}.mkv"
                    : $"录像{baseTimestamp}({currentFileIndex}).mkv";

                currentOutputPath = Path.Combine(outputDirectory, fileName);

                // 获取屏幕分辨率
                Rectangle screenBounds = Screen.PrimaryScreen.Bounds;
                int width = screenBounds.Width;
                int height = screenBounds.Height;

                // 如果需要缩放到720p
                if (height > Resolution)
                {
                    double scaleFactor = (double)Resolution / height;
                    width = (int)(width * scaleFactor);
                    height = Resolution;
                }

                // 开始前杀掉所有进程, 以防止FFmpeg进程残留影响
                var p = Process.Start(new ProcessStartInfo()
                {
                    FileName = "taskkill.exe",
                    Arguments = "/F /IM ffmpeg.exe",
                    CreateNoWindow = true,
                    UseShellExecute = false,
                });
                p.WaitForExit();

                // 启动FFmpeg进程进行录制
                ffmpegProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = ffmpegPath,
                        //Arguments = $"-f gdigrab -framerate {FrameRate} -i desktop -c:v libx264 -preset ultrafast -crf 28 -pix_fmt yuv420p -s {width}x{height} \"{currentOutputPath}\"",
                        Arguments = $"-f gdigrab -framerate 1 -i desktop -an -c:v libsvtav1 -preset 6 -crf 58 -vf \"mpdecimate,setpts=N/FRAME_RATE/TB\" -g 240 -r 1 {currentOutputPath}",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardInput = true,
                        RedirectStandardError = true,
                        RedirectStandardOutput = true,
                        WindowStyle = ProcessWindowStyle.Hidden
                    },
                    EnableRaisingEvents = true
                };

                ffmpegProcess.Start();
                ffmpegProcess.BeginErrorReadLine();

                // 监控文件大小
                StartFileSizeMonitor();

                logger.Info($"开始录制视频: {currentOutputPath}");
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "启动新录像分段时出错");
                return false;
            }
        }

        /// <summary>
        /// 监控文件大小，超过限制时自动分段
        /// </summary>
        private void StartFileSizeMonitor()
        {
            Task.Run(async () =>
            {
                try
                {
                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        await Task.Delay(10000, cancellationTokenSource.Token); // 每10秒检查一次

                        if (File.Exists(currentOutputPath))
                        {
                            FileInfo fileInfo = new FileInfo(currentOutputPath);
                            if (fileInfo.Length >= MaxFileSizeBytes)
                            {
                                logger.Info($"视频文件达到大小限制: {fileInfo.Length / (1024 * 1024)}MB，创建新分段");

                                // 停止当前录制
                                if (!ffmpegProcess.HasExited)
                                {
                                    ffmpegProcess.StandardInput.Write('q');
                                    ffmpegProcess.StandardInput.Flush();
                                    if (!ffmpegProcess.WaitForExit(10000))
                                    {
                                        ffmpegProcess.Kill();
                                    }
                                }

                                ffmpegProcess.Dispose();
                                ffmpegProcess = null;

                                // 增加索引并开始新的录制
                                currentFileIndex++;
                                if (!StartNewRecordingSegment())
                                {
                                    logger.Error("无法启动新的录像分段，停止录像");
                                    isRecording = false;
                                    break;
                                }
                            }
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // 任务被取消，正常退出
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "监控文件大小时出错");
                }
            });
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (isRecording)
            {
                StopRecording();
            }

            cancellationTokenSource?.Dispose();
        }
    }
}
