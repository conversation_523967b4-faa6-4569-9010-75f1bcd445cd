###### 编排平台接口


### 获取鉴权
# @name token
post http://localhost:5000/elsa/api/identity/login
Content-Type: application/json

  {
    "username": "admin",
    "password": "password"
  }

@result = {{token.response.body.$.accessToken}}



### 获取所有工作流
GET http://localhost:5000/elsa/api/workflow-definitions/
Host: localhost:5000
Authorization: Bearer {{result}}




### 通过name获取指定工作流
GET http://localhost:5000/elsa/api/workflow-definitions?searchTerm=ORC572&versionOptions=Published
Host: localhost:5000
Authorization: Bearer {{result}}

# 返回Json：{"items":[{"definitionId": "bcbb3cbee0c2c2f3"}]}

### 通过ID执行工作流
POST http://localhost:5000/elsa/api/workflow-definitions/951d3b150e55c289/execute
Content-Type: application/json
Host: localhost:5000
Authorization: Bearer {{result}}

{
    "input": {
        "arg": "888"
    }
}

# 返回Json：{
#   "workflowState": {
#     "definitionId": "951d3b150e55c289"
#   }
# }




### TEST
POST http://localhost:5000/elsa/api/workflow-definitions/5318d2d7c60019d4/execute
Content-Type: application/json
Host: localhost:5000
Authorization: Bearer {{result}}

{
    "input": {
        "pid": "0",
        "programName": "EXE-CommandLine",
        "priorityNumber": "11",
        "programArg": "p.name=aaaa p.age=bbbb",
        "resSelect": ""
    }
}