using Microsoft.AspNetCore.Mvc;
using RPASystem.Model;
using RPASystem.Service;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.SignalR;
using RPASystem.WebApi.Hubs;
using System.Text.Json;
using ZR.Admin.WebApi.RPA.Job;
using NLog;

namespace Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    public class OrchestrationController : ControllerBase
    {
        private readonly IOrchestrationService orchestrationService;
        private readonly IHubContext<ResourceMachineHub> resourceMachineHubContext;
        private readonly IJobTaskService jobTaskService;
        private readonly ILogger<OrchestrationController> logger;
        private readonly IExeProgramService exeProgramService;

        private static readonly Logger log = LogManager.GetCurrentClassLogger();

        public OrchestrationController(
            IOrchestrationService orchestrationService,
            IHubContext<ResourceMachineHub> resourceMachineHubContext,
            IJobTaskService jobTaskService,
            ILogger<OrchestrationController> logger,
            IExeProgramService exeProgramService)
        {
            this.orchestrationService = orchestrationService;
            this.resourceMachineHubContext = resourceMachineHubContext;
            this.jobTaskService = jobTaskService;
            this.logger = logger;
            this.exeProgramService = exeProgramService;
        }

        [HttpPost("createOrcJobTask")]
        public async Task<IActionResult> CreateOrchestrationTask([FromBody] OrchestrationTaskDto dto)
        {
            try
            {
                // 获取程序信息，判断是否为编排类型
                var program = exeProgramService.GetExeProgramByName(dto.ProgramName);
                if (program != null && program.ProgramType == ProgramTypeEnum.Orchestration)
                {
                    // 是编排任务，准备调用 Elsa 平台
                    //logger.LogInformation($"开始执行编排任务: {dto.ProgramName}");

                    // 将输入参数转换为 JSON 字符串
                    string inputParams = "{}";
                    if (!string.IsNullOrEmpty(dto.InputParameters))
                    {
                        try
                        {
                            // 如果已经是 JSON 字符串，直接使用
                            if (dto.InputParameters.StartsWith("{") && dto.InputParameters.EndsWith("}"))
                            {
                                inputParams = dto.InputParameters;
                            }
                            else
                            {
                                // 否则尝试将其转换为 JSON 对象
                                var paramDict = CommandLineParser.ParseToJObject(dto.InputParameters);
                                inputParams = paramDict.ToString();
                            }
                        }
                        catch (Exception ex)
                        {
                            //logger.LogError(ex, "参数转换为 JSON 失败");// 这种不行。 
                            logger.LogError("参数转换为 JSON 失败：" + ex.Message);
                            return BadRequest(new { Error = $"参数格式错误: {ex.Message}" });
                        }
                    }

                    try
                    {
                        // 直接调用 Elsa 平台执行工作流
                        string workflowInstanceId = await OrchestrationPlatform.ExecuteWorkflowByName(dto.ProgramName, inputParams);

                        // 直接返回工作流实例ID，不创建任务记录
                        return Ok(new { WorkflowInstanceId = workflowInstanceId });
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("调用 Elsa 平台失败：" + ex.ToString());
                        return BadRequest(new { Error = $"调用 Elsa 平台失败: {ex.Message}" });
                    }
                }

                // 不是编排任务，使用原来的方式创建任务
                var normalTaskId = await orchestrationService.CreateOrchestrationTask(dto);
                return Ok(normalTaskId);
            }
            catch (Exception ex)
            {
                logger.LogError("创建编排任务失败：" + ex.ToString());
                return BadRequest(new { Error = ex.Message });
            }
        }

        /* 调用方式： 
            n=RPA080 pid=123 rs=Resources pn=10 p.ServerIP=******* p.OriginDataPath=\\*******\test p.OutputDir=\\*******\test\output
            ParentTaskID = pid,
            ProgramName =  n,
            InputParameters = p,
            TaskPriority = pn,
            ResourceSelection = rs
        */
        // 创建任务简单版
        [HttpPost("create")]
        public async Task<IActionResult> Create()
        {
            try
            {
                using var reader = new StreamReader(Request.Body);
                string content = await reader.ReadToEndAsync();

                // 解析为 JObject
                JObject json = CommandLineParser.ParseToJObject(content);

                // 将 JObject 转换为目标对象
                OrcDto orcDto = CommandLineParser.ConvertToObject<OrcDto>(json);

                OrchestrationTaskDto dto = new OrchestrationTaskDto()
                {
                    ParentTaskID = orcDto.pid,
                    ProgramName = orcDto.n,
                    InputParameters = orcDto.p,
                    TaskPriority = orcDto.pn,
                    ResourceSelection = orcDto.rs,
                    TaskName = orcDto.t
                };

                var taskId = await orchestrationService.CreateOrchestrationTask(dto);
                await HubMessageSender.RefreshJobTasks();
                return Ok(taskId);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getOrcJobTaskStatus")]
        public async Task<IActionResult> GetOrchestrationTaskStatus(long taskId)
        {
            try
            {
                var status = await orchestrationService.GetOrchestrationTaskStatus(taskId);
                return Ok(status.Status);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getStatusByName")]
        public async Task<IActionResult> GetOrchestrationTaskStatusByName(string taskName)
        {
            try
            {
                var status = await orchestrationService.GetOrchestrationTaskStatusByName(taskName);
                return Ok(status.Status);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        public record GetStatusRequest(string[] TaskNames);
        /// <summary>
        /// 批量获取任务状态
        /// </summary>
        /// <param name="taskNames"></param>
        /// <returns>{taskNames:[{taskName:string,status:string}]}</returns>
        [HttpPost("getStatusByNames")]
        public async Task<IActionResult> GetOrchestrationTaskStatusByNames(GetStatusRequest request)
        {
            try
            {
                if (request == null || request.TaskNames == null || request.TaskNames.Length == 0)
                {
                    return BadRequest(new { Error = "任务名称不能为空" });
                }

                var result = await orchestrationService.GetOrchestrationTaskStatusByNames(request.TaskNames);

                // 转换为前端需要的格式
                var response = new
                {
                    taskNames = result.Select(kv => new
                    {
                        taskName = kv.Key,
                        status = kv.Value.Status.ToString()
                    }).ToArray()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                logger.LogError("批量获取任务状态失败" + ex.ToString());
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getReturn")]
        public async Task<IActionResult> GetTaskReturnVal(long taskId)
        {
            try
            {
                var ret = await orchestrationService.GetOrchestrationTaskStatus(taskId);
                // 兼容以前的
                if (ret?.OutputResults != null)
                {
                    try
                    {
                        // 解析 OutputResults JSON 字符串
                        var outputResults = JsonSerializer.Deserialize<JsonElement>(ret.OutputResults);
                        if (outputResults.TryGetProperty("ReturnResult", out JsonElement returnResult))
                        {
                            // 直接返回 ReturnResult 的值
                            return Ok(returnResult.GetString());
                        }
                    }
                    catch
                    {
                    }
                }

                // 如果解析失败或没有找到 ReturnResult，返回原始的 OutputResults
                return Ok(ret?.OutputResults);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getRetByName")]
        public async Task<IActionResult> GetRetByName(string taskName)
        {
            try
            {
                var ret = await orchestrationService.GetOrchestrationTaskStatusByName(taskName);
                if (ret?.OutputResults != null)
                {
                    try
                    {
                        // 解析 OutputResults JSON 字符串
                        var outputResults = JsonSerializer.Deserialize<JsonElement>(ret.OutputResults);
                        if (outputResults.TryGetProperty("ReturnResult", out JsonElement returnResult))
                        {
                            // 直接返回 ReturnResult 的值
                            return Ok(returnResult.GetString());
                        }
                    }
                    catch
                    {
                    }
                }

                // 如果解析失败或没有找到 ReturnResult，返回原始的 OutputResults
                return Ok(ret?.OutputResults);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }


        [HttpGet("setDone")]
        public async Task<IActionResult> SetDone(long taskId)
        {
            try
            {
                var ret = await jobTaskService.SetStatusDone(taskId);
                await HubMessageSender.RefreshJobTasks();
                return Ok(ret);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("RPAPlatformTaskHandle")]
        public async Task<IActionResult> RPAPlatformTaskHandleAndRun()
        {
            try
            {
                //AppDynamicToken.IsTest = true;
                //ConsumePlatformTask.IsTest = true;
                //await RPAPlatformTaskHandle.TaskService.TaskHandle.ExecuteTaskFlow();
                log.Debug("11111111111111");
                log.Warn("222222222222222222");
                log.Error("");
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }
    }
}