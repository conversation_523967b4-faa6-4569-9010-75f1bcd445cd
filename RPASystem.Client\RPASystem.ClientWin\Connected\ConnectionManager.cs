﻿using Microsoft.AspNetCore.SignalR.Client;
using NLog;
using RPASystem.Model;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using AutoUpdaterDotNET;
using System.Reflection;
using RPASystem.ClientWin.Services;
using RPASystem.ClientWin.Services.Remote;
using Newtonsoft.Json;
using RPASystem.ClientWin.ISRPA.RuntimeEnv;
using System.Text.RegularExpressions;
using RPASystem.Client.View;
using System.Windows;

namespace RPASystem.Client
{
    public class ConnectionManager
    {
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();
        private HubConnection connection;
        private CancellationTokenSource reconnectCts;
        private readonly ConcurrentDictionary<long, JobInfoModel> runningTasks = new ConcurrentDictionary<long, JobInfoModel>();
        private readonly SemaphoreSlim updateStatusLock = new SemaphoreSlim(1, 1);
        private ScreenCaptureService screenCaptureService;
        private string currentMachineName; // 保存机器名称
        private KeyboardController keyboardController; // 键盘控制器

        public event Action<string> OnLog;
        public event Action<bool> OnTaskRunningStateChanged;
        public event Action<JobInfoModel> OnTaskAdded;
        public event Action<JobInfoModel> OnTaskCompleted;
        public event Action<long> OnTaskRemoved;
        public event Action OnConnectionRefused;

        readonly string RecordingsDir = "D:\\isearch\\Recordings";

        public ConnectionManager()
        {
            keyboardController = new KeyboardController(); // 初始化键盘控制器
        }

        public async Task StartConnectionAsync(string url, string machineName)
        {
            currentMachineName = machineName; // 保存当前机器名称
            reconnectCts = new CancellationTokenSource();

            connection = new HubConnectionBuilder()
                .WithUrl($"{url}/resourceMachineHub?machineName={machineName}&version={MachineMan.Version}")
                .WithAutomaticReconnect(new CustomRetryPolicy(reconnectCts))
                .Build();

            // 初始化屏幕截图服务
            screenCaptureService = new ScreenCaptureService(async (base64Image) =>
            {
                try
                {
                    if (connection.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendScreenshot", machineName, base64Image);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "发送屏幕截图失败");
                }
            });

            RegisterHubEvents();

            await ConnectWithRetryAsync();
        }

        private void RegisterHubEvents()
        {
            // 修改：使用正确的委托类型
            connection.On<JobModel, bool>("RunTask", (jobModel) =>
            {
                // 先启动后台任务
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await HandleReceiveTaskAsync(jobModel);
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "处理接收任务异常：");
                        Log("处理接收任务异常：" + ex.Message);
                    }
                });
                return true;
            });

            connection.On<long>("StopTask", (taskId) =>
            {
                HandleStopTask(taskId);
            });

            connection.On<string>("Refused", (msg) =>
            {
                // 已注册时断开
                OnConnectionRefused?.Invoke();
                Log(msg);
            });

            connection.Reconnecting += error =>
            {
                Log($"正在重新连接...");
                return Task.CompletedTask;
            };

            connection.Reconnected += async connectionId =>
            {
                Log($"重新连接成功！");
                await UpdateResourceStatus(); //await connection.SendAsync("UpdateResourceMachineTaskStatus", GetRunningTaskIds());
            };

            connection.Closed += error =>
            {
                Log($"连接已关闭");
                return Task.CompletedTask;
            };

            connection.On<string>("NewVersionUpdate", (newVer) =>
            {
                Log("收到新版本通知");
                // 启动后台任务处理更新
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // 如果当前没有任务，添加更新任务并开始更新
                        if (runningTasks.Count != 0)
                        {
                            Log("当前有任务运行，等待其他任务完成后更新");
                            const long UPDATE_TASK_ID = 0;
                            // 在添加runningTasks更新的任务
                            runningTasks.TryAdd(UPDATE_TASK_ID, new JobInfoModel { Id = UPDATE_TASK_ID, JobTaskName = "更新任务", TopJobTaskName = "更新任务", ProgramType = ProgramTypeEnum.EXE, Parameter = "", ProgramName = "更新任务", Status = JobTaskStatusEnum.Running, StartTime = DateTime.Now, ProgramVersion = "1.0.0" });
                            // 等待直到只剩下更新任务
                            while (true)
                            {
                                // 如果只有更新1条任务，则开始更新
                                var currentTasks = runningTasks.Keys.ToList();
                                if (currentTasks.Count == 1 && currentTasks[0] == UPDATE_TASK_ID)
                                {
                                    break;
                                }
                                await Task.Delay(5000); // 每5秒检查一次
                            }
                        }
                        // 配置AutoUpdater
                        AutoUpdater.ShowSkipButton = false;
                        AutoUpdater.ShowRemindLaterButton = false;
                        AutoUpdater.LetUserSelectRemindLater = false;
                        AutoUpdater.Mandatory = true;
                        AutoUpdater.UpdateMode = Mode.ForcedDownload;
                        AutoUpdater.RunUpdateAsAdmin = false;
                        AutoUpdater.ReportErrors = false;
                        AutoUpdater.ClearAppDirectory = true;
                        var curDir = AppDomain.CurrentDomain.BaseDirectory.TrimEnd('\\');
                        var baseDir = @"C:\Program Files\RPASystem.ClientWin";//Path.GetDirectoryName(curDir);
                        var newVerDirPath = Path.Combine(baseDir, newVer);
                        // 删除当前目录以外的目录
                        if (Directory.Exists(baseDir))
                        {
                            foreach (var dir in Directory.GetDirectories(baseDir))
                            {
                                if (dir != curDir)
                                {
                                    try
                                    {
                                        Directory.Delete(dir, true);
                                    }
                                    catch (Exception ex)
                                    {
                                        Log($"删除目录失败：{dir}，错误：{ex.Message}");
                                    }
                                }
                            }
                        }
                        Directory.CreateDirectory(newVerDirPath);
                        AutoUpdater.InstallationPath = newVerDirPath;
                        AutoUpdater.ExecutablePath = Path.Combine(newVerDirPath, System.Diagnostics.Process.GetCurrentProcess().MainModule.ModuleName);
                        // 设置当前版本
                        AutoUpdater.InstalledVersion = new Version(MachineMan.Version);
                        // 设置环境变量，避免更新后启动时窗口显示
                        Environment.SetEnvironmentVariable("RPASystem_HIDE", "true", EnvironmentVariableTarget.User);
                        // 直接开始更新
                        AutoUpdater.Start($"{MachineMan.BaseUrl}/api/update/check");
                    }
                    catch (Exception ex)
                    {
                        Log($"处理更新通知异常：{ex.Message}");
                        logger.Error(ex, "处理更新通知异常");
                    }
                });
            });

            // 添加屏幕共享相关事件
            connection.On("StartScreenCapture", () =>
            {
                try
                {
                    screenCaptureService.StartCapture();
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "开始屏幕共享失败");
                }
            });

            connection.On("StopScreenCapture", () =>
            {
                try
                {
                    screenCaptureService?.StopCapture();
                    logger.Info("收到停止截图命令");
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "停止截图失败");
                }
            });

            // 添加鼠标控制相关事件
            connection.On<string, int, int>("ExecuteMouseCommand", async (button, x, y) =>
            {
                try
                {
                    logger.Info($"收到鼠标控制命令: 按钮={button}, X={x}, Y={y}");
                    var result = await RPASystem.ClientWin.Services.Remote.MouseController.ExecuteMouseCommandAsync(button, x, y);

                    // 发送结果回服务器
                    if (connection.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendMouseCommandResult", currentMachineName, result.success, result.message);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "执行鼠标控制命令失败");
                    if (connection.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendMouseCommandResult", currentMachineName, false, $"执行命令失败: {ex.Message}");
                    }
                }
            });

            // 添加鼠标滚轮控制相关事件
            connection.On<int, int, int>("ExecuteMouseWheelCommand", async (scrollAmount, x, y) =>
            {
                try
                {
                    logger.Info($"收到鼠标滚轮控制命令: 滚动量={scrollAmount}, X={x}, Y={y}");
                    var result = await MouseController.ExecuteMouseWheelCommandAsync(scrollAmount, x, y);

                    // 发送结果回服务器
                    if (connection.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendMouseCommandResult", currentMachineName, result.success, result.message);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "执行鼠标滚轮控制命令失败");
                    if (connection.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendMouseCommandResult", currentMachineName, false, $"执行滚轮命令失败: {ex.Message}");
                    }
                }
            });

            // 添加键盘控制相关事件
            connection.On<string, string, string[], int>("ExecuteKeyboardCommand", async (eventType, key, keyCombination, keyCode) =>
            {
                try
                {
                    logger.Info($"收到键盘控制命令: 事件类型={eventType}, 键={key}");
                    var result = await keyboardController.ExecuteKeyboardCommandAsync(eventType, key, keyCombination, keyCode);

                    // 发送结果回服务器
                    if (connection?.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendKeyboardCommandResult", currentMachineName, result.success, result.message);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex, "执行键盘控制命令失败");
                    if (connection?.State == HubConnectionState.Connected)
                    {
                        await connection.InvokeAsync("SendKeyboardCommandResult", currentMachineName, false, $"执行键盘命令失败: {ex.Message}");
                    }
                }
            });

            // 获得服务器消息并提示
            connection.On<string>("TaskErrorTips", (msg) =>
            {
                MsgTips.Show(msg);
            });
        }

        private async Task ConnectWithRetryAsync()
        {
            while (!reconnectCts.Token.IsCancellationRequested)
            {
                try
                {
                    await connection.StartAsync(reconnectCts.Token);
                    await UpdateResourceStatus();  // await connection.SendAsync("UpdateResourceMachineTaskStatus", GetRunningTaskIds());
                    Log("连接成功！");
                    return;
                }
                catch (Exception ex)
                {
                    Log($"连接失败: {ex.Message}，10秒后重试");
                    try
                    {
                        await Task.Delay(10000, reconnectCts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                }
            }
        }

        private void Log(string message)
        {
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss：} {message}";
            logger.Info(logMessage);
            OnLog?.Invoke(logMessage);
        }

        private async Task HandleReceiveTaskAsync(JobModel jobModel)
        {
            RunResult result;
            var jobInfo = CreateJobInfo(jobModel);
            try
            {
                Log($"开始运行任务：{jobModel.JobTaskName}   任务类型：{jobModel.ProgramType}");
                runningTasks[jobModel.Id] = jobInfo;
                OnTaskAdded?.Invoke(jobInfo);
                OnTaskRunningStateChanged?.Invoke(runningTasks.Count > 0);
                // 向服务器报告实际任务数
                await UpdateResourceStatus(); // connection.SendAsync("UpdateResourceMachineTaskStatus", GetRunningTaskIds());
                if (jobInfo.IsExclusive && !string.IsNullOrWhiteSpace(jobInfo.OutputFile))
                    VideoRecorderService.Instance.StartRecording(RecordingsDir);
                // 执行任务
                result = await ExecuteTask(jobInfo);
                // 处理结果信息，如果结果有路径则上传至服务器
                UploadResult(ref jobInfo, ref result);
                // 更新服务端任务状态
                await UpdateTaskStatus(jobInfo, result.JobTaskStatus, result);
                Log($"结束运行任务：{jobModel.JobTaskName}  返回值：{result.ToJson()}");
            }
            catch (OperationCanceledException)
            {
                result = new RunResult { IsSucceed = false, Status = 3, ReturnResult = "运行中任务被取消" };
                await UpdateTaskStatus(jobInfo, JobTaskStatusEnum.Cancelled, result);
            }
            catch (Exception ex)
            {
                result = new RunResult { IsSucceed = false, Status = 0, ReturnResult = $"任务执行失败: {ex.ToString()}" };
                logger.Error(ex, $"任务执行失败: {jobModel.JobTaskName}");
                await UpdateTaskStatus(jobInfo, JobTaskStatusEnum.Failed, result);
            }
            finally
            {
                VideoRecorderService.Instance.StopRecording();
            }
        }

        private void UploadResult(ref JobInfoModel jobInfo, ref RunResult result)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(jobInfo.OutputFile) && !string.IsNullOrWhiteSpace(result.OutputFileLocalPath))
                {
                    string ipAddress = jobInfo.UploadServerIP;
                    if (!string.IsNullOrWhiteSpace(ipAddress))
                    {
                        // 将录像文件夹中的录像文件放置本地待上传的目录中，一起上传至服务器
                        if (Directory.GetFiles(RecordingsDir)?.Length > 0)
                        {
                            VideoRecorderService.Instance.StopRecording();
                            FileOrDirOp.Perform(RecordingsDir, result.OutputFileLocalPath, FileOrDirOp.OperationType.Cut);
                        }
                        // 如果ParentTaskName和TopJobTaskName相同，证明是系统编排拆分任务类型，所以不用再加一层目录
                        var match = Regex.Match(jobInfo.JobTaskName, @"(.+)__\d+$");
                        var SplitParentTaskName = match.Success ? (match.Groups[1].Value == jobInfo.TopJobTaskName ? "" : match.Groups[1].Value) : "";
                        // 判断 TopJobTaskName 是否与 JobTaskName 相同，如果相同证明是普通任务，不用加层级
                        //var TopJobTaskName = jobInfo.TopJobTaskName == jobInfo.JobTaskName ? "" : jobInfo.TopJobTaskName;
                        // 将目录上传到服务器目录
                        var serDir = Path.Combine($@"\\{ipAddress}", "VShare", "WAutoDeploy", jobInfo.ProgramName, "OUTPUTRPASYSTEM", jobInfo.TopJobTaskName, SplitParentTaskName, jobInfo.JobTaskName);
                        FileOrDirOp.Perform(result.OutputFileLocalPath, serDir, FileOrDirOp.OperationType.Cut, 30);
                        jobInfo.OutputFile = serDir;
                        result.ReturnResult = string.IsNullOrWhiteSpace(result?.ReturnResult) ? serDir : result.ReturnResult;
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSucceed = false;
                result.ReturnResult += $"   上传文件异常: {ex.Message}";
            }
        }

        private JobInfoModel CreateJobInfo(JobModel jobModel) => new JobInfoModel
        {
            Id = jobModel.Id,
            JobTaskName = jobModel.JobTaskName,
            TopJobTaskName = jobModel.TopJobTaskName,
            ProgramType = jobModel.ProgramType,
            Parameter = jobModel.Parameter,
            ProgramName = jobModel.ProgramName,
            Status = JobTaskStatusEnum.Running,
            StartTime = DateTime.Now,
            ProgramVersion = jobModel.ProgramVersion,
            IsExclusive = jobModel.IsExclusive,
            CancellationTokenSource = new CancellationTokenSource(),
            OutputFile = jobModel.OutputFile
        };

        private async Task<RunResult> ExecuteTask(JobInfoModel jobInfo)
        {
            if (jobInfo.ProgramType == ProgramTypeEnum.RPA)
            {
                //RPARuntimeEnv.Update();
                var isrpaStarter = new ISRPAStarter(jobInfo);
                return await isrpaStarter.FastRunAsync(jobInfo.CancellationTokenSource.Token);
            }
            return await EXEStarter.RunAsync(jobInfo, jobInfo.CancellationTokenSource.Token);
        }

        private async Task UpdateTaskStatus(JobInfoModel jobInfo, JobTaskStatusEnum status, RunResult result)
        {
            try
            {
                await updateStatusLock.WaitAsync();

                jobInfo.Status = status;
                jobInfo.EndTime = DateTime.Now;

                // 通知服务器直到成功
                while (true)
                {
                    if (connection.State == HubConnectionState.Connected)
                    {
                        try
                        {
                            // 移除当前任务
                            runningTasks.TryRemove(jobInfo.Id, out _);
                            bool hasExclusiveTask = runningTasks.Values.Any(t => t.IsExclusive);
                            var taskCompletedDto = new TaskCompletedDto
                            {
                                Id = jobInfo.Id,
                                Status = status,
                                OutputResults = string.IsNullOrWhiteSpace(result.ReturnResult) && !result.IsSucceed ? result.GetErrorMessage() : result.ReturnResult,
                                RunningTaskIds = GetRunningTaskIds(jobInfo.Id),
                                HasExclusiveTask = hasExclusiveTask, // 剩下运行的任务是否有独占任务
                                OutputFile = jobInfo.OutputFile,
                                ProgramName = jobInfo.ProgramName
                            };
                            var isSuccess = await connection.InvokeAsync<bool>("TaskCompleted", taskCompletedDto, CancellationToken.None);
                            if (isSuccess)
                            {
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log($"通知服务器任务完成失败: {jobInfo.JobTaskName}, 错误: {ex.Message}");
                        }
                    }
                    await Task.Delay(10000);
                }

                // 更新本地状态
                TaskHistoryManager.SaveTaskHistory(jobInfo);
                OnTaskCompleted?.Invoke(jobInfo);
                OnTaskRemoved?.Invoke(jobInfo.Id);
                OnTaskRunningStateChanged?.Invoke(runningTasks.Count > 0);
            }
            finally
            {
                updateStatusLock.Release();
            }
        }

        public void HandleStopTask(long taskId)
        {
            Log($"收到停止任务指令：{taskId}");
            if (runningTasks.TryGetValue(taskId, out var jobInfo))
            {
                jobInfo.CancellationTokenSource.Cancel();
            }
        }

        public async Task Stop()
        {
            reconnectCts.Cancel();

            if (connection != null && connection.State == HubConnectionState.Connected)
            {
                screenCaptureService?.Dispose();
                await connection.StopAsync();
                Log("已断开连接！");
            }
        }

        public HubConnection GetConnection()
        {
            return connection;
        }

        // 获取当前任务列表的ID
        // 如果taskId为null，则获取所有任务的ID , 否则获取排除taskId的任务的ID  
        public List<long> GetRunningTaskIds(long? excludeTaskId = null)
        {
            if (excludeTaskId == null)
            {
                return runningTasks.Keys.ToList();
            }
            return runningTasks.Keys.Where(id => id != excludeTaskId).ToList();
        }

        /// <summary>
        /// 向服务器报告当前资源机状态和运行中的任务
        /// </summary>
        private async Task UpdateResourceStatus()
        {
            try
            {
                await updateStatusLock.WaitAsync();
                try
                {
                    // 检查是否有独占任务
                    bool hasExclusiveTask = runningTasks.Values.Any(t => t.IsExclusive);
                    await connection.SendAsync("UpdateResourceMachineTaskStatus", GetRunningTaskIds(), hasExclusiveTask);
                }
                catch (Exception ex)
                {
                    Log($"更新资源机状态失败: {ex.Message}");
                }
            }
            finally
            {
                updateStatusLock.Release();
            }
        }

        private class CustomRetryPolicy : IRetryPolicy
        {
            private readonly CancellationTokenSource _cts;

            public CustomRetryPolicy(CancellationTokenSource cts)
            {
                _cts = cts;
            }

            public TimeSpan? NextRetryDelay(RetryContext retryContext)
            {
                if (_cts.Token.IsCancellationRequested)
                {
                    return null;
                }
                return TimeSpan.FromSeconds(10);
            }
        }
    }
}

