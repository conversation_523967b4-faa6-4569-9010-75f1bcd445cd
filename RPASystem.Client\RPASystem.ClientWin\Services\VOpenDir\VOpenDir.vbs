On Error Resume Next  ' 启用错误处理

Function URLDecode(sEncodedURL)
    Dim i, c, sRet, sAscii
    sRet = ""
    i = 1
    Do While i <= Len(sEncodedURL)
        c = Mid(sEncodedURL, i, 1)
        If c = "%" Then
            sAscii = Mid(sEncodedURL, i + 1, 2
            c = Chr(CInt("&H" & sAscii))
            i = i + 2
        ElseIf c = "+" Then
            c = " "
        End If
        sRet = sRet & c
        i = i + 1
    Loop
    URLDecode = sRet
End Function

Set args = WScript.Arguments

If args.Count > 0 Then
    url = args(0)
    path = Replace(url, "opendir:", "")
    path = URLDecode(path)
    If Left(path, 1) = "/" Then
        path = Right(path, Len(path) - 1)
    End If
    Set shell = CreateObject("WScript.Shell")
    cmd = "explorer.exe """ & path & """"
    shell.Run cmd, 1, False
End If