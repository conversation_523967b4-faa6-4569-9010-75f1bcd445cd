﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.WebApi.Hubs;

namespace RPASystem.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ResourceMachineController : ControllerBase
    {
        private readonly IResourceMachineService resourceMachineService;
        private readonly IHubContext<ResourceMachineHub> hubContext;

        public ResourceMachineController(IResourceMachineService resourceMachineService, IHubContext<ResourceMachineHub> hubContext)
        {
            this.resourceMachineService = resourceMachineService;
            this.hubContext = hubContext;
        }

        [HttpGet]
        public async Task<IActionResult> GetResourceMachines(int pageIndex = 1, int pageSize = 10, string? searchTerm = null, bool? offlineOverSevenDays = null, [FromQuery] string[]? taskStatusFilter = null)
        {
            var (items, total) = await resourceMachineService.GetResourceMachinesAsync(pageIndex, pageSize, searchTerm, offlineOverSevenDays, taskStatusFilter?.ToList());
            return Ok(new { items, total });
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteResourceMachine(long id)
        {
            var result = await resourceMachineService.DeleteResourceMachineAsync(id);
            if (result)
            {
                return Ok(new { message = "资源机器删除成功" });
            }
            return NotFound(new { message = "未找到指定的资源机器" });
        }

        [HttpPut("{id}/type")]
        public async Task<IActionResult> UpdateMachineType(long id, [FromBody] MachineTypeEnum machineType)
        {
            var result = await resourceMachineService.UpdateMachineTypeAsync(id, machineType);
            if (result)
            {
                return Ok(new { message = "资源机类型更新成功" });
            }
            return NotFound(new { message = "未找到指定的资源机器" });
        }
    }
}
