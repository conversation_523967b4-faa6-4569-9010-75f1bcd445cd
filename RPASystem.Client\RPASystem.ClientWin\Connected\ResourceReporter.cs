﻿using Microsoft.AspNetCore.SignalR.Client;
using NLog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace RPASystem.Client
{
    public class ResourceReporter
    {
        // 静态单例实例
        public static readonly ResourceReporter Instance = new ResourceReporter();
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

        // 私有构造函数
        private ResourceReporter()
        {
        }

        public void ReportResourceInfo(HubConnection connection, string machineName)
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        if (connection.State == HubConnectionState.Connected)
                        {
                            var resourceInfo = await GetResourceInfoAsync(machineName);
                            await connection.SendAsync("UpdateResourceInfo", resourceInfo);
                        }
                        else
                        {
                            logger.Info($"无连接服务器，无法上传信息！");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, $"上传信息异常");
                    }
                    await Task.Delay(600000); // 每 600秒上报一次
                }
            });
        }

        private async Task<ResourceInfoDto> GetResourceInfoAsync(string machineName)
        {
            return new ResourceInfoDto
            {
                MachineName = machineName,
                ComputerName = Environment.MachineName,
                CpuUsage = (float)Math.Round(await GetCpuUsageAsync()),
                MemoryUsage = (float)Math.Round(await GetMemoryUsageAsync()),
                DiskUsage = GetDiskUsage()
            };
        }

        private Dictionary<string,string> GetDiskUsage()
        {
            var diskUsage = new Dictionary<string, string>();

            foreach (var drive in DriveInfo.GetDrives())
            {
                if (drive.IsReady)
                {
                    float usedSpace = drive.TotalSize - drive.AvailableFreeSpace;
                    float usagePercentage = (usedSpace / drive.TotalSize) * 100;
                    diskUsage[drive.Name.TrimEnd('\\')] = $"{Math.Round(usagePercentage)}%";
                }
            }
            return diskUsage;
        }


        private async Task<float> GetCpuUsageAsync()
        {
            return await GetPerformanceCounterValueAsync("Processor", "% Processor Time", "_Total");
        }

        private async Task<float> GetMemoryUsageAsync()
        {
            return await GetPerformanceCounterValueAsync("Memory", "% Committed Bytes In Use");
        }

        private async Task<float> GetPerformanceCounterValueAsync(string categoryName, string counterName, string instanceName = null)
        {
            using (PerformanceCounter counter = new PerformanceCounter(categoryName, counterName, instanceName))
            {
                counter.NextValue();
                await Task.Delay(1000); // 等待 1 秒以获取稳定值
                return counter.NextValue();
            }
        }
    }
}