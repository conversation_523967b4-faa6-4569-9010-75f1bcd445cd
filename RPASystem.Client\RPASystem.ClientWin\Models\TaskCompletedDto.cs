using System.Collections.Generic;

namespace RPASystem.Model
{
    public class TaskCompletedDto
    {
        public long Id { get; set; }
        public JobTaskStatusEnum Status { get; set; }
        public string OutputResults { get; set; }
        public List<long> RunningTaskIds { get; set; } = new List<long>();
        public bool HasExclusiveTask { get; set; }
        public string OutputFile { get; set; }
        public string ProgramName { get; set; }
    }
} 