using System;
using System.ComponentModel.DataAnnotations;

namespace RPASystem.Model
{
    /// <summary>
    /// 系统配置DTO
    /// </summary>
    public class SystemConfigDto
    {
        public long? ID { get; set; }

        /// <summary>
        /// 配置键名
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string ConfigKey { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string ConfigValue { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        [MaxLength(50)]
        public string ConfigGroup { get; set; }

        /// <summary>
        /// 是否系统内置
        /// </summary>
        public bool IsSystem { get; set; }
    }

    /// <summary>
    /// 批量更新配置DTO
    /// </summary>
    public class BatchUpdateConfigDto
    {
        public List<SystemConfigDto> Configs { get; set; }
    }
} 