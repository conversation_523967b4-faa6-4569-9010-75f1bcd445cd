using Infrastructure;
using System.IO;
using ZR.Infrastructure;

namespace RPASystem.Service.Common
{
    public static class FileUploadManager
    {
        private const string UPLOAD_DATA_FOLDER = "UploadData";

        /// <summary>
        /// 获取上传文件的真实物理路径
        /// </summary>
        /// <param name="type">文件类型，用于分子目录，如 ProgramPackage, ClientPackage</param>
        /// <param name="fileName">带扩展名的文件名</param>
        /// <returns>文件的完整物理路径</returns>
        public static string GetFileRealPath(string type, string fileName)
        {
            var fileSharePath = InternalApp.Configuration?["FileSharePath"];
            string basePath;

            if (!string.IsNullOrEmpty(fileSharePath))
            {
                basePath = fileSharePath;
            }
            else
            {
                basePath = AppDomain.CurrentDomain.BaseDirectory;
            }

            var directoryPath = Path.Combine(basePath, UPLOAD_DATA_FOLDER, type);
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            return Path.Combine(directoryPath, fileName);
        }
    }
} 