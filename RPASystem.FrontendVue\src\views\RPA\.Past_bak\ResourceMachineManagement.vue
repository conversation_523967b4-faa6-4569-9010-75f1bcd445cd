<template>
  <div>
    <div class="header-section">
      <!-- 搜索框 -->
      <el-input
        v-model="searchTerm"
        placeholder="搜索资源机名"
        @input="handleSearch"
        style="width: 300px;"
      >
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>

      <!-- 任务状态筛选 -->
      <div class="filter-section">
        <span class="filter-label">任务状态：</span>
        <el-checkbox-group v-model="taskStatusFilter" @change="handleSearch">
          <el-checkbox-button label="0">空闲</el-checkbox-button>
          <el-checkbox-button label="1">任务运行中</el-checkbox-button>
          <el-checkbox-button label="2">离线</el-checkbox-button>
        </el-checkbox-group>
      </div>

      <!-- 离线时间筛选 -->
      <div class="filter-section">
        <el-checkbox-button v-model="offlineOverSevenDays" @change="handleSearch">离线超过7天</el-checkbox-button>
      </div>

      <!-- 版本更新区域 -->
      <div class="version-update">
        <span class="version-text">最新版本：{{ latestVersion || '未发布' }}</span>
        <el-upload
          class="upload-demo"
          :http-request="customUpload"
          :show-file-list="false"
          accept=".zip"
        >
          <el-button type="primary">上传新版本</el-button>
        </el-upload>
      </div>
    </div>

    <el-table :data="resourceMachines" style="width: 100%" border>
      <el-table-column prop="machineName" label="资源机名" />
      <el-table-column prop="computerName" label="计算机名" />
      <el-table-column label="资源机类型" width="120">
        <template #default="scope">
          <el-popover
            placement="bottom"
            :width="120"
            trigger="click"
            v-model:visible="scope.row._popoverVisible"
            @show="scope.row._typeTemp = scope.row.machineType"
            @hide="scope.row._typeTemp = scope.row.machineType"
          >
            <template #reference>
              <span class="clickable-type">{{ getMachineType(scope.row.machineType) }}</span>
            </template>
            <div class="type-options">
              <div
                v-for="type in ['在线执行机', '服务机', '普通机']"
                :key="type"
                class="type-option"
                :class="{ 'selected': type === scope.row._typeTemp }"
                @click="handleTypeSelect(scope.row, type)"
              >
                {{ type }}
              </div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="ipAddress" label="IP地址"></el-table-column>
      <el-table-column prop="clientVersion" label="客户端版本"></el-table-column>
      <el-table-column prop="isLatestVersion" label="最新版本" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isLatestVersion ? 'success' : 'danger'" size="small">
            {{ scope.row.isLatestVersion ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastActivityTime" label="最后活动时间"></el-table-column>
      <el-table-column prop="taskStatus" label="运行状态" width="120">
        <template #default="scope">
          <el-tag :type="getTaskStatusType(scope.row.taskStatus)" size="small">
            {{ getTaskStatus(scope.row.taskStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="cpuUsage" label="CPU使用率">
        <template #default="scope">
          {{ scope.row.cpuUsage }}%
        </template>
      </el-table-column>
      <el-table-column prop="memoryUsage" label="内存使用率">
        <template #default="scope">
          {{ scope.row.memoryUsage }}%
        </template>
      </el-table-column>
      <el-table-column label="磁盘使用率">
        <template #default="scope">
          <div v-for="(usage, drive) in JSON.parse(scope.row.diskUsage)" :key="drive">
            {{ drive }}  {{ usage }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <div class="operation-buttons">
            <el-button type="primary" size="small" @click="handleRemote(scope.row)">远程桌面</el-button>
            <el-button type="danger" size="small" @click="confirmDelete(scope.row.id)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 使用远程桌面组件 -->
    <remote-desktop
      v-if="selectedMachine"
      v-model:visible="remoteDialogVisible"
      :machine-name="selectedMachine.machineName"
    />
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import * as signalR from '@microsoft/signalr';
import { ElMessage, ElMessageBox } from 'element-plus';
import RemoteDesktop from '@/views/components/RPA/RemoteDesktop.vue';

export default {
  name: 'ResourceMachineManagement',
  components: {
    RemoteDesktop
  },
  setup() {
    const resourceMachines = ref([]);
    const searchTerm = ref('');
    const latestVersion = ref('');
    const selectedMachine = ref(null);
    const remoteDialogVisible = ref(false);
    const taskStatusFilter = ref([]);
    const offlineOverSevenDays = ref(false);
    let connection;

    const fetchResourceMachines = async () => {
      try {
        const response = await axios.get('/api/resourcemachine', {
          params: { 
            searchTerm: searchTerm.value,
            taskStatusFilter: taskStatusFilter.value,
            offlineOverSevenDays: offlineOverSevenDays.value
          }
        });
        let machines = response.data.map(machine => ({
          ...machine,
          diskUsage: machine.diskUsage || '{}'
        }));

        if (taskStatusFilter.value.length > 0) {
          machines = machines.filter(machine => 
            taskStatusFilter.value.includes(machine.taskStatus.toString())
          );
        }

        resourceMachines.value = machines;
      } catch (error) {
        console.error('获取资源机列表失败:', error);
      }
    };

    const fetchLatestVersion = async () => {
      try {
        const response = await axios.get('/api/update/version');
        latestVersion.value = response.data.version;
      } catch (error) {
        console.error('获取最新版本失败:', error);
      }
    };

    const customUpload = async (options) => {
      try {
        const formData = new FormData();
        formData.append('file', options.file);

        const response = await axios.post('/api/update/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        ElMessage.success('更新包上传成功');
        latestVersion.value = response.data.version;
      } catch (error) {
        ElMessage.error('更新包上传失败：' + error.message);
      }
    };

    const handleSearch = () => {
      fetchResourceMachines();
    };

    const deleteResourceMachine = async (id) => {
      try {
        await axios.delete(`/api/resourcemachine/${id}`);
        fetchResourceMachines();
      } catch (error) {
        console.error('删除资源机失败:', error);
      }
    };

    const getTaskStatus = (status) => {
      const statusMap = {
        0: '空闲',
        1: '任务运行中',
        2: '离线'
      };
      return statusMap[status] || '未知状态';
    };

    const getTaskStatusType = (status) => {
      const typeMap = {
        0: 'success',  // 空闲 - 绿色
        1: 'warning',  // 任务运行中 - 黄色
        2: 'info'      // 离线 - 灰色
      };
      return typeMap[status] || 'info';
    };

    const getMachineType = (type) => {
      const typeMap = {
        0: '在线执行机',
        1: '服务机',
        2: '普通机'
      };
      return typeMap[type] || '未知类型';
    };

    const handleRemote = (machine) => {
      selectedMachine.value = machine;
      remoteDialogVisible.value = true;
    };

    const confirmDelete = (id) => {
      ElMessageBox.confirm('确定要删除该资源机吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteResourceMachine(id);
      }).catch(() => {
        // 用户取消删除
      });
    };

    const handleTypeSelect = async (row, type) => {
      // 将类型文字转换为对应的枚举值
      const typeValueMap = {
        '在线执行机': 0,
        '服务机': 1,
        '普通机': 2
      };
      
      try {
        // 立即关闭下拉列表
        row._popoverVisible = false;
        
        await axios.put(`/api/resourcemachine/${row.id}/type`, typeValueMap[type], {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        row.machineType = typeValueMap[type];
        ElMessage.success('资源机类型更新成功');
      } catch (error) {
        ElMessage.error('资源机类型更新失败');
        await fetchResourceMachines();
      }
    };

    onMounted(async () => {
      await fetchResourceMachines();
      await fetchLatestVersion();
      
      connection = new signalR.HubConnectionBuilder()
        .withUrl(`/resourceMachineHub`)
        .withHubProtocol(new signalR.JsonHubProtocol())
        .withAutomaticReconnect()
        .configureLogging(signalR.LogLevel.Information)
        .build();

      connection.on('RefreshResourceMachines', () => {
        fetchResourceMachines();
      });

      try {
        await connection.start();
      } catch (err) {
        console.error('SignalR Connection Error: ', err);
      }
    });

    onUnmounted(() => {
      if (connection) {
        connection.stop();
      }
    });

    return {
      resourceMachines,
      searchTerm,
      latestVersion,
      selectedMachine,
      remoteDialogVisible,
      handleSearch,
      deleteResourceMachine,
      handleRemote,
      getTaskStatus,
      getTaskStatusType,
      getMachineType,
      customUpload,
      taskStatusFilter,
      offlineOverSevenDays,
      confirmDelete,
      handleTypeSelect
    };
  },
};
</script>

<style scoped>
.header-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
}

.version-update {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.version-text {
  font-size: 14px;
  color: #606266;
}

.upload-demo {
  display: inline-block;
}

.operation-buttons {
  display: flex;
  gap: 8px;
}

.clickable-type {
  cursor: pointer;
  color: #409EFF;
}

.clickable-type:hover {
  text-decoration: underline;
}

.type-options {
  padding: 8px 0;
}

.type-option {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.type-option:hover {
  background-color: #f5f7fa;
}

.type-option.selected {
  color: #409EFF;
  font-weight: bold;
}
</style>
