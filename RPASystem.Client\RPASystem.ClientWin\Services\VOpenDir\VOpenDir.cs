using System;
using System.IO;
using Microsoft.Win32;
using System.Reflection;
using RPASystem.ClientWin;
using RPASystem.Client.Properties;

namespace RPASystem.ClientWin.Services.VOpenDir
{
    /// <summary>
    /// 打开目录协议处理工具类
    /// </summary>
    public static class VOpenDir
    {
        // 目标EXE文件路径
        private const string ExeTargetPath = @"C:\Windows\VOpenDir.exe";

        /// <summary>
        /// 确保打开目录协议处理器已安装
        /// </summary>
        /// <returns>安装操作是否成功</returns>
        public static bool EnsureInstalled()
        {
            // 如果已安装则返回true
            if (IsInstalled())
            {
                return true;
            }

            // 否则进行安装
            return Install();
        }

        /// <summary>
        /// 安装打开目录协议处理器
        /// </summary>
        /// <returns>安装是否成功</returns>
        public static bool Install()
        {
            try
            {
                // 从资源中提取EXE文件
                byte[] exeBytes = Resources.VOpenDir;
                if (exeBytes == null || exeBytes.Length == 0)
                {
                    throw new InvalidOperationException("资源中未找到VOpenDir.EXE文件");
                }

                // 保存到Windows目录
                File.WriteAllBytes(ExeTargetPath, exeBytes);

                // 使用C#类库直接创建注册表项
                using (RegistryKey openDirKey = Registry.ClassesRoot.CreateSubKey("opendir"))
                {
                    openDirKey.SetValue("", "URL:Open Directory Protocol");
                    openDirKey.SetValue("URL Protocol", "");

                    using (RegistryKey iconKey = openDirKey.CreateSubKey("DefaultIcon"))
                    {
                        iconKey.SetValue("", "explorer.exe,1");
                    }

                    // 创建shell/open/command键
                    using (RegistryKey shellKey = openDirKey.CreateSubKey("shell"))
                    {
                        using (RegistryKey openKey = shellKey.CreateSubKey("open"))
                        {
                            using (RegistryKey commandKey = openKey.CreateSubKey("command"))
                            {
                                // 直接使用EXE文件，不通过wscript
                                commandKey.SetValue("", $"\"{ExeTargetPath}\" \"%1\"");
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"安装打开目录协议处理器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查协议处理器是否已安装
        /// </summary>
        /// <returns>是否已安装</returns>
        public static bool IsInstalled()
        {
            // 检查EXE文件是否存在
            bool exeExists = File.Exists(ExeTargetPath);

            // 检查注册表项是否存在并且命令值是否正确
            bool regCorrect = false;
            try
            {
                using (RegistryKey key = Registry.ClassesRoot.OpenSubKey(@"opendir\shell\open\command"))
                {
                    if (key != null)
                    {
                        string commandValue = key.GetValue("") as string;
                        regCorrect = commandValue != null && commandValue.Contains(ExeTargetPath);
                    }
                }
            }
            catch
            {
                regCorrect = false;
            }

            return exeExists && regCorrect;
        }

        /// <summary>
        /// 卸载打开目录协议处理器
        /// </summary>
        /// <returns>卸载是否成功</returns>
        public static bool Uninstall()
        {
            try
            {
                // 删除EXE文件
                if (File.Exists(ExeTargetPath))
                {
                    File.Delete(ExeTargetPath);
                }

                // 删除注册表项
                Registry.ClassesRoot.DeleteSubKeyTree("opendir", false);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"卸载打开目录协议处理器失败: {ex.Message}");
                return false;
            }
        }
    }
}
