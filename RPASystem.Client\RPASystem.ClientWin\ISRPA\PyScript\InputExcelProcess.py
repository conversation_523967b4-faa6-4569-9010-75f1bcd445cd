#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA Excel合并工具

功能：将RPA运行后生成的异常Excel和警告Excel合并到输入Excel中
"""

import os
from openpyxl import load_workbook, Workbook


def merge_input_excel_with_exception_warning(main_column_name, input_excel_path, exception_warning_dir, merged_excel_path=None):
    """
    将异常Excel和警告Excel中的数据合并到输入Excel中

    参数:
    - main_column_name: 输入Excel中的主列列名（如"编号"）
    - input_excel_path: 输入Excel文件的路径
    - exception_warning_dir: 异常Excel和警告Excel所在的目录
    - merged_excel_path: 合并后保存的Excel路径，如果为空则直接覆盖输入Excel

    返回值: 无

    异常:
    - FileNotFoundError: 输入Excel文件不存在
    - ValueError: 主列列名在输入Excel中不存在
    - Exception: 其他读取或写入错误

    说明:
    - 异常Excel文件名固定为: ExceptionsLog.xlsx
    - 警告Excel文件名固定为: WarningLog.xlsx
    - 如果异常或警告Excel不存在，程序会继续执行
    - 合并规则：
      * 如果主列值在异常Excel中存在，状态设为"异常"
      * 如果主列值在警告Excel中存在，状态设为"警告"
      * 如果都不存在，状态设为"已完成"
    - 会自动添加三列：RPA状态、PRA信息、RPA截图
    """

    # 检查输入Excel是否存在
    if not os.path.exists(input_excel_path):
        raise FileNotFoundError(f"输入Excel文件不存在: {input_excel_path}")

    # 读取输入Excel
    print(f"正在读取输入Excel: {input_excel_path}")
    try:
        input_wb = load_workbook(input_excel_path)
        input_ws = input_wb.active
    except Exception as e:
        raise Exception(f"读取输入Excel失败: {e}")

    # 读取表头和数据
    headers = []
    input_data = []

    # 读取表头
    for cell in input_ws[1]:
        headers.append(cell.value)

    # 读取数据行
    for row in input_ws.iter_rows(min_row=2, values_only=True):
        input_data.append(list(row))

    # 检查是否存在必要的列，如果不存在则添加
    required_columns = ["RPA状态", "PRA信息", "RPA截图"]
    # 按照正确的顺序添加列
    for i, col in enumerate(required_columns):
        if col not in headers:
            headers.insert(i, col)  # 按顺序插入
            # 为现有数据行添加空值
            for data_row in input_data:
                data_row.insert(i, None)
            print(f"添加列: {col}")

    # 找到主列的索引
    if main_column_name not in headers:
        raise ValueError(f"在输入Excel中找不到主列: {main_column_name}")
    main_column_index = headers.index(main_column_name)

    # 读取异常Excel
    exception_excel_path = os.path.join(exception_warning_dir, "ExceptionsLog.xlsx")
    exception_data = {}
    if os.path.exists(exception_excel_path):
        print(f"正在读取异常Excel: {exception_excel_path}")
        try:
            exception_wb = load_workbook(exception_excel_path)
            exception_ws = exception_wb.active

            # 读取异常数据（跳过表头）
            for row in exception_ws.iter_rows(min_row=2, values_only=True):
                if row[0]:  # KEY不为空
                    exception_data[row[0]] = {
                        'msg': row[1],  # EXCEPTION_MSG
                        'img': row[2]   # EXCEPTION_IMG
                    }
        except Exception as e:
            print(f"读取异常Excel时出错: {e}")
    else:
        print(f"异常Excel不存在: {exception_excel_path}")

    # 读取警告Excel
    warning_excel_path = os.path.join(exception_warning_dir, "WarningLog.xlsx")
    warning_data = {}
    if os.path.exists(warning_excel_path):
        print(f"正在读取警告Excel: {warning_excel_path}")
        try:
            warning_wb = load_workbook(warning_excel_path)
            warning_ws = warning_wb.active

            # 读取警告数据（跳过表头）
            for row in warning_ws.iter_rows(min_row=2, values_only=True):
                if row[0]:  # KEY不为空
                    warning_data[row[0]] = {
                        'msg': row[1],  # EXCEPTION_MSG
                        'img': row[2]   # EXCEPTION_IMG
                    }
        except Exception as e:
            print(f"读取警告Excel时出错: {e}")
    else:
        print(f"警告Excel不存在: {warning_excel_path}")

    # 找到RPA相关列的索引
    rpa_status_index = headers.index("RPA状态")
    pra_info_index = headers.index("PRA信息")
    rpa_screenshot_index = headers.index("RPA截图")

    # 合并数据
    print("正在合并数据...")
    for data_row in input_data:
        main_column_value = data_row[main_column_index]

        if main_column_value in exception_data:
            # 设置为异常状态
            data_row[rpa_status_index] = '异常'
            data_row[pra_info_index] = exception_data[main_column_value]['msg']
            data_row[rpa_screenshot_index] = exception_data[main_column_value]['img']
            print(f"设置 {main_column_value} 为异常状态")
        elif main_column_value in warning_data:
            # 设置为警告状态
            data_row[rpa_status_index] = '警告'
            data_row[pra_info_index] = warning_data[main_column_value]['msg']
            data_row[rpa_screenshot_index] = warning_data[main_column_value]['img']
            print(f"设置 {main_column_value} 为警告状态")
        else:
            # 设置为已完成状态
            data_row[rpa_status_index] = '已完成'
            data_row[pra_info_index] = None
            data_row[rpa_screenshot_index] = None
            print(f"设置 {main_column_value} 为已完成状态")

    # 保存结果
    output_path = merged_excel_path if merged_excel_path else input_excel_path
    print(f"正在保存结果到: {output_path}")

    try:
        # 创建新的工作簿
        output_wb = Workbook()
        output_ws = output_wb.active

        # 写入表头
        output_ws.append(headers)

        # 写入数据
        for data_row in input_data:
            output_ws.append(data_row)

        # 保存文件
        output_wb.save(output_path)
        print(f"合并完成！结果已保存到: {output_path}")
    except Exception as e:
        raise Exception(f"保存Excel文件失败: {e}")


if __name__ == "__main__":
    # 示例用法
    try:
        merge_input_excel_with_exception_warning(
            main_column_name="编号",
            input_excel_path=r"Test\Input\RPA_List-演示.xlsx",
            exception_warning_dir=r"Test\Log",
            merged_excel_path=r"Test\RPA_List-演示-最终合并结果.xlsx"
        )
    except Exception as e:
        print(f"程序执行出错: {e}")