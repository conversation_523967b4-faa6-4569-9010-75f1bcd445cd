<template>
  <div class="resource-pool-manager">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <div class="left-section">
        <el-input
          v-model="searchQuery"
          placeholder="请输入资源池名称搜索"
          style="width: 300px;"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
      <div class="right-section">
        <el-button type="primary" @click="showAddDialog">新增资源池</el-button>
      </div>
    </div>

    <!-- 资源池列表 -->
    <el-table :data="resourcePools" style="width: 100%" v-loading="loading">
      <el-table-column prop="poolName" label="资源池名称" width="180" />
      <el-table-column prop="description" label="描述" />
      <el-table-column label="包含资源机" min-width="300">
        <template #default="scope">
          <el-tag 
            v-for="machine in scope.row.machineList" 
            :key="machine"
            class="mx-1"
            size="small"
          >
            {{ machine }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" width="180" :formatter="formatDateTime" />
      <el-table-column prop="updatedTime" label="修改时间" width="180" :formatter="formatDateTime" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑资源池' : '新增资源池'"
      width="1200px"
      destroy-on-close
    >
      <el-form :model="currentPool" label-width="100px" :rules="rules" ref="formRef">
        <el-form-item label="资源池名称" prop="poolName">
          <el-input v-model="currentPool.poolName" placeholder="请输入资源池名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="currentPool.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入描述信息"
          />
        </el-form-item>
        <el-form-item label="资源机" prop="selectedMachines">
          <el-transfer
            v-model="selectedMachines"
            :data="allMachines"
            filterable
            :titles="['可用资源机', '已选资源机']"
            :props="{
              key: 'machineName',
              label: 'machineName'
            }"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="resourcepoolmanager">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  listResourcePools, 
  listResourceMachines, 
  createResourcePool, 
  updateResourcePool, 
  deleteResourcePool 
} from '@/api/RPA/ResourcePoolManager'

// 数据定义
const resourcePools = ref([])
const allMachines = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const selectedMachines = ref([])
const formRef = ref(null)
const searchQuery = ref('')
const loading = ref(false)

const currentPool = ref({
  poolName: '',
  description: '',
  resourceMachineNames: ''
})

// 表单验证规则
const rules = {
  poolName: [
    { required: true, message: '请输入资源池名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  selectedMachines: [
    { 
      validator: (rule, value, callback) => {
        if (selectedMachines.value.length === 0) {
          callback(new Error('请至少选择一个资源机'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 获取所有资源池
const fetchResourcePools = async () => {
  loading.value = true
  try {
    const response = await listResourcePools()
    resourcePools.value = response.data
    loading.value = false
  } catch (error) {
    ElMessage.error('获取资源池列表失败')
    console.error('获取资源池列表失败:', error)
    loading.value = false
  }
}

// 获取所有资源机
const fetchResourceMachines = async () => {
  try {
    const response = await listResourceMachines()
    allMachines.value = response.data.map(machine => ({
      machineName: machine.machineName,
      key: machine.machineName
    }))
  } catch (error) {
    ElMessage.error('获取资源机列表失败')
    console.error('获取资源机列表失败:', error)
  }
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  currentPool.value = {
    poolName: '',
    description: '',
    resourceMachineNames: ''
  }
  selectedMachines.value = []
  dialogVisible.value = true
}

// 显示编辑对话框
const handleEdit = (row) => {
  isEdit.value = true
  currentPool.value = { ...row }
  selectedMachines.value = row.machineList || []
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该资源池吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    await deleteResourcePool(row.id)
    ElMessage.success('删除成功')
    await fetchResourcePools()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 更新资源机名称列表
    currentPool.value.resourceMachineNames = selectedMachines.value.join('|')

    if (isEdit.value) {
      await updateResourcePool(currentPool.value)
    } else {
      await createResourcePool(currentPool.value)
    }
    
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    await fetchResourcePools()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
    }
  }
}

// 格式化日期时间
const formatDateTime = (row, column, cellValue) => {
  if (!cellValue) return ''
  return new Date(cellValue).toLocaleString()
}

// 搜索处理
const handleSearch = () => {
  fetchResourcePools()
}

// 页面加载时初始化数据
onMounted(async () => {
  await fetchResourceMachines()
  await fetchResourcePools()
})
</script>

<style scoped>
.resource-pool-manager {
  padding: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mx-1 {
  margin: 0 4px;
}

:deep(.el-transfer) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-transfer__buttons) {
  display: inline-flex;
  flex-direction: row !important;
  padding: 0 30px;
  align-items: center;
}

:deep(.el-transfer__button) {
  display: inline-block;
  margin: 0 5px !important;
}

:deep(.el-transfer-panel) {
  width: 45%;
  min-width: 420px;
}

:deep(.el-transfer-panel__body) {
  height: 400px;
}

:deep(.el-tag) {
  margin: 2px;
}

:deep(.el-transfer__buttons .el-button + .el-button) {
  margin-left: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-form-item__content) {
  display: flex;
  justify-content: flex-start;
}
</style> 