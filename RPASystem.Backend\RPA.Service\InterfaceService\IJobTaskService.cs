using RPASystem.Model;

namespace RPASystem.Service
{
    public interface IJobTaskService
    {
        Task UpdateJobTaskStatusAsync(long id, JobTaskStatusEnum status, string machineName = null, string outputResults = null, string outputFile = null);
        Task BatchUpdateJobTaskStatusAsync(List<long> taskIds, JobTaskStatusEnum status);
        List<ViewJobTaskExeProgram> GetAllJobTasksWithExePrograms();
        Task<long> CreateJobTaskAsync(JobTask jobTask);
        Task<JobTask> GetJobTaskByIdAsync(long id);
        Task<JobTask> GetJobTaskByNameAsync(string taskName);
        Task<List<ViewJobTaskExeProgram>> GetPendingTasksWithProgramAsync();
        Task<string> GetTopTaskNameAsync(long taskId);
        Task<(List<ViewJobTaskExeProgram> Items, int TotalCount)> GetJobTasksPagedAsync(JobTaskQueryParams queryParams);
        Task<(List<ViewJobTaskExeProgram> Items, int TotalCount)> GetSubTasksPagedAsync(SubTaskQueryParams queryParams);
        Task<List<string>> GetAvailableResourceMachinesAsync(string resourceSelection);
        Task<List<JobTask>> GetTasksToStopAsync(long taskId);
        Task<bool> UpdateJobTaskWithChildrenAsync(long taskId, JobTaskUpdateDto updateDto);
        bool DeleteJobTask(long id);
        public Task<bool> SetStatusDone(long taskId);
        Task<bool> RetryJobTaskAsync(long id);
        Task<List<ViewJobTaskExeProgram>> GetTasksForAutoRetryAsync();
    }
} 