

自动更新插件功能

在ISRPA\RuntimeEnv目录下新建一个RPARuntimeEnv.cs实现功能。我希望用最简单的方式实现。逻辑可以硬编码在代码里。

调用一个静态方法执行更新操作，如：RPARuntimeEnv.Update()

1. 获取Plugin1目录下带版本的zip包（有多个时取版本号最新一个）
2. 取到文件名,如：1.0.1   将名称与C:\iS-RPA\push\Plugin 目录下的Version.txt文件里第一行的值对比.
3. 如果不同则删除当前目录下所有文件, 将压缩包里的数据解压到C:\iS-RPA\push\Plugin目录下, 并重新将最新版本号写入Version.txt


Plugin2, Plugin3 更新的逻辑一样,但路径不一样,Plugin2 可能在D盘, 可以在任何方. 我希望可以在代码里方便的添加 Plugin2, Plugin3 , Plugin4 等
建议给我个Dictionary键值对, key是RuntimeEnv目录下的目录名. Vuale是目标路径

更新时可能会无法删除文件,里需要重试删除. 重试1个小时仍然失败则抛异常.




在 MsgTips.cs 下写一个弹出窗口类，我调用 MsgTips.Show("失败！"); 即弹出窗口。窗口界面我已图好。如图
多次调用，只会弹出一次窗口。如果窗口没有关闭，显示最后一次显示内容。
在Windows桌面弹出右下角。窗口置顶显示。
点击 "查看详情" 用默认浏览器打开链接，并关闭窗口。也可能通过窗口上角X点击关闭窗口。