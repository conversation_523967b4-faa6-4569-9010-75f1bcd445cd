﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace ZR.Admin.WebApi.RPA.Job
{
    public static class OrchestrationPlatform
    {
        /*

        # 获取鉴权
        - 失效时间1小时

        # 通过name执行工作流
        - 通过编排名称name获取指定工作流ID
        - 通过ID执行工作流
        - 返回ID

        */

        //private static readonly string ElsaApiBaseUrl = "http://10.73.133.57:5000/elsa/api";
        private static readonly string ElsaApiBaseUrl = "http://127.0.0.1:5001/elsa/api";
        private static string accessToken;
        private static DateTime tokenExpireTime = DateTime.MinValue;

        /// <summary>
        /// 获取鉴权令牌
        /// </summary>
        /// <returns>访问令牌</returns>
        public static async Task<string> GetAccessToken()
        {
            // 如果令牌有效且未过期，直接返回
            if (!string.IsNullOrEmpty(accessToken) && DateTime.Now < tokenExpireTime)
                return accessToken;

            using var httpClient = new HttpClient();
            var url = $"{ElsaApiBaseUrl}/identity/login";
            var content = new StringContent(JsonConvert.SerializeObject(new { username = "admin", password = "password" }), 
                Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(url, content);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseBody);

            accessToken = tokenResponse["accessToken"];
            tokenExpireTime = DateTime.Now.AddHours(1); // 设置过期时间为1小时

            return accessToken;
        }

        /// <summary>
        /// 通过工作流名称查找对应的工作流定义ID
        /// </summary>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流定义ID</returns>
        public static async Task<string> GetWorkflowIdByName(string workflowName)
        {
            if (string.IsNullOrEmpty(workflowName))
                throw new ArgumentException("工作流名称不能为空", nameof(workflowName));

            var token = await GetAccessToken();
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var url = $"{ElsaApiBaseUrl}/workflow-definitions?searchTerm={workflowName}&versionOptions=Published";
            var response = await httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<WorkflowDefinitionsResponse>(responseBody);

            if (result.Items == null || result.Items.Count == 0)
                throw new Exception($"未找到名称为 {workflowName} 的工作流");

            return result.Items[0].DefinitionId;
        }

        /// <summary>
        /// 通过ID执行工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="input">输入参数JSON字符串</param>
        /// <returns>执行结果</returns>
        public static async Task<string> ExecuteWorkflowById(string workflowId, string input = null)
        {
            if (string.IsNullOrEmpty(workflowId))
                throw new ArgumentException("工作流ID不能为空", nameof(workflowId));

            var token = await GetAccessToken();
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var url = $"{ElsaApiBaseUrl}/workflow-definitions/{workflowId}/execute";
            
            // 构建请求内容
            string requestJson;
            if (string.IsNullOrEmpty(input))
            {
                requestJson = "{}";
            }
            else
            {
                // 直接拼接input参数到请求中
                requestJson = "{\"input\": " + input + "}";
            }

            var content = new StringContent(requestJson, Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync(url, content);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<WorkflowExecuteResponse>(responseBody);

            return result?.WorkflowState?.ID;
        }

        /// <summary>
        /// 通过工作流名称执行工作流
        /// </summary>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="input">输入参数JSON字符串</param>
        /// <returns>工作流实例ID</returns>
        public static async Task<string> ExecuteWorkflowByName(string workflowName, string input = null)
        {
            var workflowId = await GetWorkflowIdByName(workflowName);
            return await ExecuteWorkflowById(workflowId, input);
        }

        // 响应模型类
        private class WorkflowDefinitionsResponse
        {
            [JsonProperty("items")]
            public List<WorkflowDefinition> Items { get; set; }
        }

        private class WorkflowDefinition
        {
            [JsonProperty("definitionId")]
            public string DefinitionId { get; set; }
        }

        private class WorkflowExecuteResponse
        {
            [JsonProperty("workflowState")]
            public WorkflowState WorkflowState { get; set; }
        }

        private class WorkflowState
        {
            [JsonProperty("definitionId")]
            public string DefinitionId { get; set; }
            [JsonProperty("id")]
            public string ID { get; set; }
        }
    }
}
