<template>
  <div class="resource-machine-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>资源机配置</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="handleSave">保存配置</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="configForm" label-width="200px" :rules="rules" ref="formRef">
        <el-form-item label="最大同时运行任务数量" prop="maxConcurrentTasks">
          <el-input-number 
            v-model="configForm.maxConcurrentTasks" 
            :min="1" 
            :max="20" 
            style="width: 180px;"
          />
          <div class="form-item-help">设置资源机可同时运行的最大任务数量</div>
        </el-form-item>
        
        <el-form-item label="内存阈值重启百分比" prop="memoryThreshold">
          <el-slider 
            v-model="configForm.memoryThreshold" 
            :min="50" 
            :max="95" 
            :step="5"
            show-stops
            style="width: 300px;"
          >
            <template #default="{ modelValue }">
              <div class="slider-value">{{ modelValue }}%</div>
            </template>
          </el-slider>
          <div class="form-item-help">当资源机内存使用率超过此阈值时将触发重启</div>
        </el-form-item>
        
        <el-form-item label="文件存储服务器IP" prop="fileStorageServers">
          <el-tag
            v-for="(server, index) in configForm.fileStorageServers"
            :key="index"
            class="server-tag"
            closable
            @close="removeServer(index)"
          >
            {{ server }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="serverInputRef"
            v-model="serverInputValue"
            class="server-input"
            size="small"
            @keyup.enter="addServer"
            @blur="addServer"
          />
          <el-button v-else class="button-new-tag" size="small" @click="showInput">
            + 添加服务器
          </el-button>
          <div class="form-item-help">设置任务输出文件存储服务器IP地址，支持多个</div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup name="resourcemachineconfig">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getConfigsByGroup,
  getConfigValue,
  batchUpdateConfigs,
  listAllConfigs
} from '@/api/RPA/SystemConfigManager'

// 表单引用
const formRef = ref(null)

// 表单数据
const configForm = ref({
  maxConcurrentTasks: 3,
  memoryThreshold: 90,
  fileStorageServers: []
})

// 表单验证规则
const rules = {
  maxConcurrentTasks: [
    { required: true, message: '请设置最大同时运行任务数量', trigger: 'blur' }
  ],
  memoryThreshold: [
    { required: true, message: '请设置内存阈值百分比', trigger: 'change' }
  ]
}

// 服务器IP输入相关
const inputVisible = ref(false)
const serverInputValue = ref('')
const serverInputRef = ref(null)

// 获取配置
const fetchConfigs = async () => {
  try {
    // 获取资源机相关配置
    const response = await getConfigsByGroup('ResourceMachine')
    const machineConfigs = response.data || []
    
    // 获取文件存储相关配置
    const fileStorageResponse = await getConfigsByGroup('FileStorage')
    const fileStorageConfigs = fileStorageResponse.data || []
    
    // 合并所有配置
    const allConfigs = [...machineConfigs, ...fileStorageConfigs]
    
    // 设置表单值
    allConfigs.forEach(config => {
      if (config.configKey === 'ResourceMachine.MaxConcurrentTasks') {
        configForm.value.maxConcurrentTasks = parseInt(config.configValue) || 3
      } else if (config.configKey === 'ResourceMachine.MemoryThreshold') {
        configForm.value.memoryThreshold = parseInt(config.configValue) || 90
      } else if (config.configKey === 'FileStorage.ServerIPs') {
        configForm.value.fileStorageServers = config.configValue.split(',').filter(ip => ip.trim() !== '')
      }
    })
  } catch (error) {
    ElMessage.error('获取配置失败')
    console.error('获取配置失败:', error)
  }
}

// 保存配置
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 准备批量更新的配置
    const configs = [
      {
        configKey: 'ResourceMachine.MaxConcurrentTasks',
        configValue: configForm.value.maxConcurrentTasks.toString(),
        configGroup: 'ResourceMachine',
        description: '资源机最大同时运行任务数量'
      },
      {
        configKey: 'ResourceMachine.MemoryThreshold',
        configValue: configForm.value.memoryThreshold.toString(),
        configGroup: 'ResourceMachine',
        description: '内存使用率阈值（百分比），超过此值将触发重启'
      },
      {
        configKey: 'FileStorage.ServerIPs',
        configValue: configForm.value.fileStorageServers.join(','),
        configGroup: 'FileStorage',
        description: '文件存储服务器IP列表，多个IP用逗号分隔'
      }
    ]
    
    // 获取所有配置以检查ID
    const allConfigs = await listAllConfigs()
    const existingConfigs = allConfigs.data || []
    
    // 为已存在的配置添加ID
    configs.forEach(config => {
      const existing = existingConfigs.find(c => c.configKey === config.configKey)
      if (existing) {
        config.ID = existing.ID
      }
    })
    
    await batchUpdateConfigs({ configs })
    ElMessage.success('保存配置成功')
  } catch (error) {
    ElMessage.error('保存配置失败')
    console.error('保存配置失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  fetchConfigs()
}

// 显示服务器IP输入框
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    serverInputRef.value?.focus()
  })
}

// 添加服务器IP
const addServer = () => {
  if (serverInputValue.value) {
    if (isValidIP(serverInputValue.value)) {
      if (!configForm.value.fileStorageServers.includes(serverInputValue.value)) {
        configForm.value.fileStorageServers.push(serverInputValue.value)
      } else {
        ElMessage.warning('该服务器IP已存在')
      }
    } else {
      ElMessage.error('请输入有效的IP地址')
    }
  }
  inputVisible.value = false
  serverInputValue.value = ''
}

// 移除服务器IP
const removeServer = (index) => {
  configForm.value.fileStorageServers.splice(index, 1)
}

// 验证IP地址格式
const isValidIP = (ip) => {
  const regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return regex.test(ip)
}

// 组件挂载时获取配置
onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.resource-machine-config {
  padding: 20px;
}

.config-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-item-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.server-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.server-input {
  width: 200px;
  display: inline-block;
  vertical-align: bottom;
  margin-right: 10px;
}

.button-new-tag {
  margin-bottom: 10px;
}

.slider-value {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: #606266;
}
</style> 