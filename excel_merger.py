import os
from openpyxl import load_workbook, Workbook


def 输入Excel合并异常警告Excel(主列列名, 输入Excel路径, 异常警告Excel目录, 合并后Excel路径=None):
    """
    将异常Excel和警告Excel中的数据合并到输入Excel中

    参数:
    - 主列列名: 输入Excel中的主列列名（如"编号"）
    - 输入Excel路径: 输入Excel文件的路径
    - 异常警告Excel目录: 异常Excel和警告Excel所在的目录
    - 合并后Excel路径: 合并后保存的Excel路径，如果为空则直接覆盖输入Excel
    """

    # 检查输入Excel是否存在
    if not os.path.exists(输入Excel路径):
        raise FileNotFoundError(f"输入Excel文件不存在: {输入Excel路径}")

    # 读取输入Excel
    print(f"正在读取输入Excel: {输入Excel路径}")
    try:
        input_wb = load_workbook(输入Excel路径)
        input_ws = input_wb.active
    except Exception as e:
        raise Exception(f"读取输入Excel失败: {e}")

    # 读取表头和数据
    headers = []
    input_data = []

    # 读取表头
    for cell in input_ws[1]:
        headers.append(cell.value)

    # 读取数据行
    for row in input_ws.iter_rows(min_row=2, values_only=True):
        input_data.append(list(row))

    # 检查是否存在必要的列，如果不存在则添加
    required_columns = ["RPA状态", "PRA信息", "RPA截图"]
    # 按照正确的顺序添加列
    for i, col in enumerate(required_columns):
        if col not in headers:
            headers.insert(i, col)  # 按顺序插入
            # 为现有数据行添加空值
            for data_row in input_data:
                data_row.insert(i, None)
            print(f"添加列: {col}")

    # 找到主列的索引
    if 主列列名 not in headers:
        raise ValueError(f"在输入Excel中找不到主列: {主列列名}")
    主列索引 = headers.index(主列列名)

    # 读取异常Excel
    异常Excel路径 = os.path.join(异常警告Excel目录, "ExceptionsLog.xlsx")
    异常数据 = {}
    if os.path.exists(异常Excel路径):
        print(f"正在读取异常Excel: {异常Excel路径}")
        exception_wb = load_workbook(异常Excel路径)
        exception_ws = exception_wb.active

        # 读取异常数据（跳过表头）
        for row in exception_ws.iter_rows(min_row=2, values_only=True):
            if row[0]:  # KEY不为空
                异常数据[row[0]] = {
                    'msg': row[1],  # EXCEPTION_MSG
                    'img': row[2]   # EXCEPTION_IMG
                }
    else:
        print(f"异常Excel不存在: {异常Excel路径}")

    # 读取警告Excel
    警告Excel路径 = os.path.join(异常警告Excel目录, "WarningLog.xlsx")
    警告数据 = {}
    if os.path.exists(警告Excel路径):
        print(f"正在读取警告Excel: {警告Excel路径}")
        warning_wb = load_workbook(警告Excel路径)
        warning_ws = warning_wb.active

        # 读取警告数据（跳过表头）
        for row in warning_ws.iter_rows(min_row=2, values_only=True):
            if row[0]:  # KEY不为空
                警告数据[row[0]] = {
                    'msg': row[1],  # EXCEPTION_MSG
                    'img': row[2]   # EXCEPTION_IMG
                }
    else:
        print(f"警告Excel不存在: {警告Excel路径}")

    # 找到RPA相关列的索引
    RPA状态索引 = headers.index("RPA状态")
    PRA信息索引 = headers.index("PRA信息")
    RPA截图索引 = headers.index("RPA截图")

    # 合并数据
    print("正在合并数据...")
    for data_row in input_data:
        主列值 = data_row[主列索引]

        if 主列值 in 异常数据:
            # 设置为异常状态
            data_row[RPA状态索引] = '异常'
            data_row[PRA信息索引] = 异常数据[主列值]['msg']
            data_row[RPA截图索引] = 异常数据[主列值]['img']
            print(f"设置 {主列值} 为异常状态")
        elif 主列值 in 警告数据:
            # 设置为警告状态
            data_row[RPA状态索引] = '警告'
            data_row[PRA信息索引] = 警告数据[主列值]['msg']
            data_row[RPA截图索引] = 警告数据[主列值]['img']
            print(f"设置 {主列值} 为警告状态")
        else:
            # 设置为已完成状态
            data_row[RPA状态索引] = '已完成'
            data_row[PRA信息索引] = None
            data_row[RPA截图索引] = None
            print(f"设置 {主列值} 为已完成状态")

    # 保存结果
    输出路径 = 合并后Excel路径 if 合并后Excel路径 else 输入Excel路径
    print(f"正在保存结果到: {输出路径}")

    # 创建新的工作簿
    output_wb = Workbook()
    output_ws = output_wb.active

    # 写入表头
    output_ws.append(headers)

    # 写入数据
    for data_row in input_data:
        output_ws.append(data_row)

    # 保存文件
    output_wb.save(输出路径)
    print(f"合并完成！结果已保存到: {输出路径}")


if __name__ == "__main__":
    # 测试函数
    输入Excel合并异常警告Excel(
        主列列名="编号",
        输入Excel路径=r"Test\Input\RPA_List-演示.xlsx",
        异常警告Excel目录=r"Test\Log",
        合并后Excel路径=r"Test\RPA_List-演示-合并结果.xlsx"
    )
