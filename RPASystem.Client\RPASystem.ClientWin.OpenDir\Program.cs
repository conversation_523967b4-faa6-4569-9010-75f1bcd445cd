﻿using System;
using System.Diagnostics;
using System.IO;
using System.Net;

namespace RPASystem.ClientWin.OpenDir
{
    internal class Program
    {
        static void Main(string[] args)
        {
            try
            {
                if (args.Length > 0)
                {
                    string url = args[0];
                    
                    // 移除协议前缀
                    string path = url.Replace("opendir:", "");
                    
                    // URL解码 - 正确处理中文字符
                    path = WebUtility.UrlDecode(path);
                    
                    // 如果路径以/开头，则移除
                    if (path.StartsWith("/"))
                    {
                        path = path.Substring(1);
                    }
                    
                    // 确保网络路径格式正确
                    if (path.StartsWith("\\") && !path.StartsWith("\\\\"))
                    {
                        path = "\\" + path;
                    }

                    // 记录日志以便调试
                    string logPath = Path.Combine(Path.GetTempPath(), "opendir_log.txt");
                    File.AppendAllText(logPath, $"{DateTime.Now}: 打开路径: {path}\n");

                    // 打开文件夹
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = $"\"{path}\"",
                        UseShellExecute = false
                    };

                    Process.Start(startInfo);
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                string errorLogPath = Path.Combine(Path.GetTempPath(), "opendir_error.txt");
                File.AppendAllText(errorLogPath, $"{DateTime.Now}: 错误: {ex.Message}\n{ex.StackTrace}\n");
            }
        }
    }
}
