using System.Text.Json;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.Service;
using Infrastructure.Attribute;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace RPASystem.Service;

public partial class JobTaskService
{
    /// <summary>
    /// 处理RPA任务的合并操作,
    /// task = 父任务对象
    /// </summary>
    public async Task<JobTaskStatusEnum> MergeDirFilesAsync(JobTask parentTask, JobTaskStatusEnum jobTaskStatusEnum, IQueryable<JobTask> siblingTasks)
    {
        var mergeTaskName = parentTask.TaskName + "_merge";
        // 状态如果不为成功直接返回，只处理成功状态的, 如果已经生成合并任务，也返回
        if (jobTaskStatusEnum != JobTaskStatusEnum.Success || string.IsNullOrEmpty(parentTask.OutputFile))
        {
            return jobTaskStatusEnum;
        }
        // 如果有合并,则不处理返回
        if (siblingTasks.Any(t => t.TaskName == mergeTaskName))
            return jobTaskStatusEnum;
        try
        {
            // 解析输入参数
            var inputParams = JsonSerializer.Deserialize<Dictionary<string, string>>(parentTask.InputParameters ?? "{}");

            // 检查必要参数
            if (!inputParams.TryGetValue("MergeType", out var mergeTypeStr))
            {
                return jobTaskStatusEnum;
            }
            // 从parentTask.OutputFile里提取IP地址，OutputFile数据通常是：\\***********\XXX\YY
            var serverIP = string.Empty;
            if (!string.IsNullOrEmpty(parentTask.OutputFile))
            {
                var ipMatch = System.Text.RegularExpressions.Regex.Match(parentTask.OutputFile, @"\\\\([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})");
                if (ipMatch.Success)
                {
                    serverIP = ipMatch.Groups[1].Value;
                }
            }

            // 如果ServerIP为空，抛出异常
            if (string.IsNullOrEmpty(serverIP))
            {
                throw new Exception("合并任务的ServerIP不能为空");
            }

            // 创建合并任务的参数
            var orchestrationTask = new OrchestrationTaskDto
            {
                TaskName = mergeTaskName,
                ParentTaskID = parentTask.ID,
                ProgramName = "RPASystem.MergeTool",
                ResourceSelection = serverIP,
                TaskPriority = parentTask.Priority,
                InputParameters = JsonSerializer.Serialize(new
                {
                    targetPath = Path.Combine(parentTask.OutputFile, "Output"),
                    sourcePaths = $@"{parentTask.OutputFile}\{parentTask.TaskName}__{{X}}\Output",
                    processType = mergeTypeStr
                })
            };

            // 使用OrchestrationService创建合并任务
            await orchestrationService.CreateOrchestrationTask(orchestrationTask);

            // 创建成功后返回运行状态
            return JobTaskStatusEnum.Running;
        }
        catch (Exception ex)
        {
            parentTask.OutputResults = $@"创建合并任务失败: {ex.Message}";
            //task.Status = JobTaskStatusEnum.Failed;
            return JobTaskStatusEnum.Failed;
        }
    }

    /// <summary>
    /// 更新父任务的输出地址
    /// </summary>
    /// <param name="parentTask"></param>
    /// <param name="subTask"></param>
    /// <returns></returns>
    public async Task UpdateOutputFileForParentTaskAsync(JobTask parentTask, JobTask subTask)
    {
        // 父任务是空的，子任务状态是成功且输出路径不是空的
        if (string.IsNullOrEmpty(parentTask.OutputFile) && subTask.Status == JobTaskStatusEnum.Success && !string.IsNullOrEmpty(subTask.OutputFile))
        {
            // 判断是否是服务路径如：\\***********\XXX\YY
            // 如果是服务路径，则将YY目录改成固定的"Output"目录并保存到父任务OutputFile里。不是服务器路径则不处理
            var serverPathMatch = System.Text.RegularExpressions.Regex.Match(subTask.OutputFile, @"^\\\\[^\\]+\\[^\\]+\\(.+)$");
            if (serverPathMatch.Success)
            {
                var serverPrefix = subTask.OutputFile.Substring(0, subTask.OutputFile.TrimEnd('\\').LastIndexOf('\\'));
                parentTask.OutputFile = $"{serverPrefix}"; // \\Output
                parentTask.OutputResults = $"{serverPrefix}";
                await db.SaveChangesAsync();
            }
        }
    }
}
