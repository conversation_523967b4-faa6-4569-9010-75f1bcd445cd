# EXE管理系统功能模块介绍

## 概述

EXE管理系统是一个综合平台，用于管理EXE程序、资源机和任务。该系统允许在后台将EXE任务分发到指定的资源机（运行Windows系统的客户端）上执行，并对其进行监控和调度。

---

## 目录

- [EXE管理系统功能模块介绍](#exe管理系统功能模块介绍)
  - [概述](#概述)
  - [目录](#目录)
  - [WEB后台页面功能](#web后台页面功能)
    - [EXE程序管理](#exe程序管理)
    - [资源机监控管理](#资源机监控管理)
    - [任务管理](#任务管理)
    - [资源池管理](#资源池管理)
    - [RPA账号凭证管理](#rpa账号凭证管理)
  - [资源机管理 - 客户端](#资源机管理---客户端)
    - [通讯技术](#通讯技术)
    - [重连功能](#重连功能)
    - [前端资源机管理页面实时更新数据功能](#前端资源机管理页面实时更新数据功能)
  - [EXE程序管理详细说明](#exe程序管理详细说明)
    - [后端字段信息](#后端字段信息)
    - [新增或修改页面输入参数Json的保存](#新增或修改页面输入参数json的保存)
    - [新增或修改页面，程序类型：编排](#新增或修改页面程序类型编排)
  - [资源机监控管理详细说明](#资源机监控管理详细说明)
    - [后端字段信息](#后端字段信息-1)
    - [资源机类型说明](#资源机类型说明)
    - [客户端连接注册流程技术细节](#客户端连接注册流程技术细节)
  - [任务管理详细说明](#任务管理详细说明)
    - [后端字段信息](#后端字段信息-2)
    - [任务创建和调度流程技术细节](#任务创建和调度流程技术细节)
      - [任务创建](#任务创建)
      - [任务分发和调度](#任务分发和调度)
    - [前端列表显示字段](#前端列表显示字段)
    - [停止功能描述](#停止功能描述)
    - [新增任务页面显示字段](#新增任务页面显示字段)
      - [前端页面创建任务时流程](#前端页面创建任务时流程)
    - [主任务名称的自动生成规则](#主任务名称的自动生成规则)
      - [子任务名称的自动生成规则](#子任务名称的自动生成规则)
    - [程序参数解析和保存规则](#程序参数解析和保存规则)
    - [指定资源机和资源池的字段说明：](#指定资源机和资源池的字段说明)
    - [任务类型说明](#任务类型说明)
      - [普通任务（类型值：0）](#普通任务类型值0)
      - [编排任务（类型值：1）](#编排任务类型值1)
      - [系统编排拆分任务（类型值：2）](#系统编排拆分任务类型值2)
      - [针对于系统编排拆分任务的任态处理](#针对于系统编排拆分任务的任态处理)
    - [任务接口](#任务接口)
      - [创建任意任务](#创建任意任务)
      - [获取编排任务状态](#获取编排任务状态)
  - [附加功能](#附加功能)
    - [文件管理（文件存储功能）](#文件管理文件存储功能)
    - [文件存储数据库字段](#文件存储数据库字段)
    - [Web服务初始化](#web服务初始化)
    - [RPA的运行状态：异常、警告、已完成](#rpa的运行状态异常警告已完成)
      - [记录信息的Python方法：](#记录信息的python方法)
    - [RPA任务重跑功能设计](#rpa任务重跑功能设计)
    - [任务管理-输出文件](#任务管理-输出文件)
    - [定时任务管理](#定时任务管理)
    - [独占运行](#独占运行)
    - [配置表功能](#配置表功能)
    - [程序管理-失败通知资源机](#程序管理-失败通知资源机)
    - [客户端-接受失败信息](#客户端-接受失败信息)
    - [录像功能](#录像功能)
    - [资源机自动重启](#资源机自动重启)
    - [任务自动重试](#任务自动重试)
    - [程序参数自动生成，上传自动填充参数](#程序参数自动生成上传自动填充参数)
    - [程序管理-存储方式](#程序管理-存储方式)
    - [任务列表-查询逻辑](#任务列表-查询逻辑)

---




## WEB后台页面功能

### EXE程序管理

**主要用途**：存储和管理EXE程序的ZIP压缩包。

**主要功能**：

- **CRUD操作界面**：提供创建、读取、更新、删除EXE程序的界面。
- **资源机灵活分配**：可以为每个EXE程序选择多个特定的资源机，或允许在任意资源机上运行。

### 资源机监控管理

**主要用途**：监控资源机的状态和性能，确保任务执行的可靠性。

**主要功能**：

- **资源机注册**：客户端连接时自动注册，同一资源机名不可重复。
- **状态监控**：实时显示资源机的运行状态（空闲、任务运行中、离线）。
- **当前任务显示**：展示资源机当前运行的EXE程序名称（支持多个EXE同时运行，由客户端主动上报）。
- **性能数据收集**：客户端定期推送CPU、内存、磁盘使用情况，服务端更新数据库。
- **任务分发**：向资源机推送EXE文件并执行。

### 任务管理
**主要用途**：负责任务的创建、分发、调度和监控。
**主要功能**：
- **创建EXE任务**：用户可以选择EXE程序，填写运行参数，创建新任务。
- **任务分发执行**：创建任务后，如果有空闲的资源机，则将EXE下发至资源机运行；如果没有空闲资源机，则等待至有空闲资源机为止。
- **监控任务**：实时监控任务的运行状态（待运行、运行中、运行成功、运行失败）。
- **优先级设置**：待运行任务可设置优先级，数值越大优先级越高。
- **停止功能**：运行中的任务可手动停止。
- **重试功能**：任务运行成功或失败后，可以重试运行。
- **指定资源机运行**：将待运行的任务推送到绑定的资源机上运行（如果资源机空闲）。
- **管理任务队列**：处理待执行任务、进行中的任务及任务重试。
---

### 资源池管理
**主要用途**：一个资源池可以设置多个资源机，就是将多个资源机设置成一个资源池。
**主要功能**：
- **资源池管理**：前端页面显示资源池，并可以添加、修改和删除资源池。
**设计相关**：  
- 数据库表：ResourcePool 。它与ResourceMachines表的MachineName字段关联。
---

### RPA账号凭证管理
**主要用途**：用于启动RPA时选择账号, 只是发下RPA其中的一个参数。
**主要功能**：
- CRUD操作界面：提供创建、读取、更新、删除账号的界面。
- 存储账号信息。
- 提供API接口，通过用户名获取密文
**数据库设计**：  
- 表名: RpaAccountInfo
- 字段:
  - AccountName: 账号名称
  - CipherText: 密文
  - Remark: 备注
  - CreateTime: 创建时间
  - LastUpdateTime: 最后更新时间
---

## 资源机管理 - 客户端

### 通讯技术
- **通讯方式**：使用SignalR与服务端进行双向实时通讯。

### 重连功能
- **描述**：当客户端程序与服务器失去连接时，自动尝试重连，确保任务不中断。

### 前端资源机管理页面实时更新数据功能
- **技术细节**：
  - **服务端推送**：后端在更新资源机数据时，使用SignalR推送更新通知给前端。
  - **前端响应**：前端页面接收到通知后，自动刷新数据，无需手动点击查询按钮。
  - **数据更新频率**：根据实际需求设置，可实现实时或定期更新。

**主要功能**：
1. **连接服务端**：
   - 设置连接服务器的IP地址和客户端名称（服务端根据客户端名称推送任务）。
2. **执行EXE任务**：
   - 执行服务端下发的EXE程序。服务端推送ZIP压缩包，客户端解压后运行根目录的`Main.exe`程序。
3. **资源机信息上报**：
   - 定时（每60秒）上报资源机信息，包括CPU、内存、磁盘使用情况。

**其它特殊功能**：
- 收到RPA类型任务，对参数的细节处理：
  - 当参数名有InputFile时，通过参数值（ID）从接口获取二进制文件，保存至指定位置，保存成功后从Json中移除参数。
    - 二进制文件保存至指定位置：保存到属性"rpaCase.InputPath"，文件固定命名："RPA_List-1.xlsx"
  - 当参数名有ServerIP时，将Output目录的内容保存到ServerIP的指定目录。
    - 将参数保存为变量后从Json中移除参数。
    - ServerIP的指定目录："\\\\{ServerIP}\\VShare\\WAutoDeploy\\{rpaCase.Name}\\OUTPUTRPASYSTEM\\{JobTaskName}\\{SubJobTaskName}\\"
  - 将所有参数转换成Key，Value 保存至指定新建的Excel文件中。
    - 新建的Excel路径："{rpaCase.ConfigPath}\\RPA_Config.xlsx"
- RPA类型任务结束后：
  - 将现有EXCEL文件InputFile通过接口上传至后端。也就是将"RPA_List-1.xlsx"文件上传。
    - 文件管理接口：FileStorageController下UpdateFile(long id, IFormFile file, [FromForm] string remark)方法。remark="{年月日时分秒}已更新文件。"

- 客户端返回值：
  - 返回给服务器返回值的以Json字符串的方式返回。
  - EXE和RPA的Josn字符串返回字段：
    ``` Json
    {
      "IsSucceed" : true , //是否成功
      "ReturnResult" : "字符串返回值" ,
      "ErrorMessage" : "错误信息" , // （如果为空时，则不在json字符串里显示）
      "Status" : 1,  //  0异常失败 ， 1 成功 ，2 警告 (只有RPA有此字段)
    }
    ```
  - 返回值处理逻辑：
    - 得到EXE或RPA返回值Josn后，将对应的值填充到RunResult对象.
    - 如果EXE或RPA运行完后续逻辑出异常,则改写RunResult对象中的IsSucceed,并将异常追加至ErrorMessage(可能EXE或RPA的返回值已经有错误值.)
    - 在最后TaskCompleted上传任务状态时,根据IsSucceed确定JobTaskStatusEnum的值.

- 日志功能：
  - 用NLog记录日志，Logs目录下分类型建文件夹存放（Error、Info、Warning、Debug）
  - 单个最大日志文件50MB

---

## EXE程序管理详细说明

### 后端字段信息
- **程序名称**
- **版本号**（仅用于显示）
- **程序输入参数**（JSON格式）
- **是否独占资源机**（是否独占一台资源机运行）
- **程序包ZIP**（二进制数据）
- **程序类型**（如RPA、EXE、OCR）
- **创建时间**
- **最后更新时间**
- **备注**
- **资源机选择** 指定资源机和资源池运行，多个用|分隔 . 作用:默认值, 当任务创建时将此值赋值给任务的ResourceSelection字段. 页面效果请参考:###  指定资源机和资源池的字段说明：
- **程序版本** 版本号自动累加，从*******开始，4位只保持个位，当*******时，下个版本为*******，当EXE或RPA上传程序包时版本号自动+1

### 新增或修改页面输入参数Json的保存
- **用途说明**：程序输入参数用于在新建任务时，方便用户输入参数。
- **默认参数模板**：
  - **新增页面默认类型**：打开新增页面时，默认类型为RPA，RPA类型有默认的输入参数模板。
  - **EXE类型默认参数**：选择EXE类型时，程序输入参数默认为空。
- **页面功能**：
  - **动态添加/删除参数**：提供按钮动态添加或删除参数行。
  - **参数行排版**：每行包括参数类型、参数名称、参数描述，删除按钮位于行末。
- **参数类型**：
  - 支持类型：`string`、`int`、`bool`、`file`、`select`及特殊类型：`ResourceSelection`, `RpaCredentials`。
- **输入参数JSON格式**：
  - 示例：
    ```json
    [
      {
        "ParametersName": "IsDebug",
        "ParametersType": "bool",
        "ParametersDescription": "是否",
        "DefaultValue": "false", // 默认值
        "IsRequired": true // 是否必填
      },
      {
        "ParametersName": "",
        "ParametersType": "string",
        "ParametersDescription": "参数",
        "DefaultValue": "", // 默认值
        "IsRequired": true // 是否必填
      },
      {
        "ParametersName": "InputFile",
        "ParametersType": "file",
        "ParametersDescription": "输入文件",
        "DefaultValue": "", // 默认值
        "IsRequired": true // 是否必填
      },
      {
        "ParametersName": "ChooseType",
        "ParametersType": "select",
        "ParametersDescription": "选择类型，可多选,选择项目，用'|'分隔",
        "ParametersOptions": "1,2",  // 1=单选, 2=多选
        "ParametersSelectValue": "",  // 选择项目，用'|'分隔, 如:AAA|BBB|CCC
        "DefaultValue": "", // 默认值
        "IsRequired": true // 是否必填
      },
      {
        "ParametersName": "Number",
        "ParametersType": "int",
        "ParametersDescription": "数量",
        "DefaultValue": "10", // 默认值
        "IsRequired": true // 是否必填
      },
      {
        "ParametersName": "UserName",
        "ParametersType": "RpaCredentials", // RPA账号凭证
        "ParametersDescription": "选择RPA账号凭证",
        "DefaultValue": "", // 默认值
        "IsRequired": true // 是否必填
      }
    ]
    ```
- **RPA类型默认输入参数模板**：
  ```json
  [
    {
      "ParametersName": "InputFile",
      "ParametersType": "file",
      "ParametersDescription": "输入文件"
    },
    {
      "ParametersName": "ServerIP",
      "ParametersType": "string",
      "ParametersDescription": "服务器IP"
    },
    {
      "ParametersName": "UserName",
      "ParametersType": "RpaCredentials",
      "ParametersDescription": "用户名"
    }
  ]
  ```

---

### 新增或修改页面，程序类型：编排
- 选择编排任务时，上传文件包和是否独占隐藏，其它字段正常显示。
- 默认输入参数与RPA类型相同。

## 资源机监控管理详细说明

### 后端字段信息

- **资源机名**
- **资源机的计算机名**
- **资源机类型**（在线执行机、服务机、普通机）
- **主机IP地址**
- **客户端版本**
- **最后活动时间**
- **资源机当前运行的EXE程序名称**（空闲时为空）
- **资源机运行状态**（空闲、运行中、离线）
- **CPU使用情况**
- **内存使用情况**
- **磁盘使用情况**

### 资源机类型说明

- **在线执行机**：专门用于执行任务的资源机，可以运行所有类型的任务，当任务的"资源选择"没有指定时，可以自动分配。
- **服务机**：只用于运行特定的EXE程序的资源机，当任务的"资源选择"有指定时，才分配。也就是说，服务机只有任务的"资源选择"有指定时，才分配。
- **普通机**：普通的资源机，不能执行下发任务执行。

### 客户端连接注册流程技术细节

1. **客户端连接**：
   - 客户端在连接时提供以下信息：
     - 唯一的资源机名称
     - 资源机类型
     - 主机IP地址
     - 客户端版本
2. **服务端检查**：
   - 服务端检查该资源机名称是否已注册。
3. **注册或更新**：
   - **未注册**：新建资源机记录，状态设为在线。
   - **已注册**：更新现有记录的最后活动时间，状态设为在线。
4. **重复名称处理**：
   - 如果资源机名称重复，拒绝连接并提示更改名称。

---

## 任务管理详细说明

### 后端字段信息

- **任务ID**
- **父任务ID**（用于编排任务）
- **任务类型**（普通任务=0、编排任务=1、系统编排任务=2）
- **程序ID**
- **任务名称**
- **任务优先级**
- **任务创建时间**
- **运行开始时间**
- **运行结束时间**
- **分配的资源机名称**
- **程序输入参数**（JSON格式）
- **程序输出结果**（JSON格式）
- **任务状态**（待运行、运行中、运行成功、运行失败、任务中止）
- **备注**
- **资源机选择** 指定资源机和资源池运行，多个用|分隔

### 任务创建和调度流程技术细节

#### 任务创建

1. **前端表单**：提供任务创建表单，用户选择EXE程序并填写参数。
2. **任务记录生成**：后端生成新任务记录，初始状态为"待运行"。

#### 任务分发和调度

1. **定期检查**：服务器端定期（如每3秒）查询数据库中待运行的任务。
2. **资源机检查**：
   - 检查是否有空闲的资源机。
   - 根据任务的资源机选择（指定的资源机或任意资源机）进行匹配。
3. **任务分配**：
   - 根据任务优先级，从高到低进行任务分配。
   - 将任务信息推送至选定的资源机，并更新任务状态为“运行中”。
4. **任务执行**：
   - 资源机收到任务后，开始执行并实时上报状态。
5. **任务完成**：
   - 任务执行成功或失败后，资源机上报结果，服务端更新任务状态和输出结果。

### 前端列表显示字段

- **任务ID**
- **程序名称**（通过程序ID关联获取）
- **任务名称**（唯一索引）
- **任务优先级**
- **任务创建时间**
- **运行开始时间**
- **运行结束时间**
- **分配的资源机名称**
- **程序输入参数**（JSON）
- **程序输出结果**（JSON）
- **任务状态**（待运行、运行中、运行成功、运行失败、任务中止）
- **备注**

### 停止功能描述

- **按钮显示**：当任务状态为"运行中"时，前端页面显示"停止"按钮。
- **操作流程**：
  1. 用户点击"停止"按钮。
  2. 系统找到对应的资源机，发送停止指令。
  3. 资源机接收到指令，停止当前任务（杀掉进程）。
  4. 资源机反馈任务已停止，服务端更新任务状态为"任务中止"。
- **停止处理状态**：
  - 如果任务状态为"运行中"或"待运行"，则可以停止。如果状态为其它状态，则不停止。
  - 如果资源机不为空且任务状态为"运行中"时，则下发执行机停止的任务指令。
  - 如果当前任务有子任务和子子任务，将所��子任务和子子任务的状态设置为"取消"。如果状态不等于"运行中"或"待运行"状态，则不改变状态。

### 新增任务页面显示字段

- **任务优先级**：输入数值，默认值为10。
- **程序列表**：下拉选择可用的EXE程序。
- **程序参数**：根据选择的程序，动态显示需要填写的参数。

#### 前端页面创建任务时流程

1. **任务名称生成**：
   - 如果任务名称为空，则自动生成，遵循任务名称的自动生成规则。
2. **程序选择与参数解析**：
   - 选择程序后，读取EXE程序的输入参数（JSON格式）。
   - 前端解析参数，动态生成对应的输入控件。
3. **参数填写**：
   - 用户根据提示填写参数值。
4. **任务保存**：
   - 将填写的参数值以JSON格式保存到"程序输入参数"字段中。

### 主任务名称的自动生成规则
- **主任务名称格式**：`程序类型 + 当前日期YYYYMMDD + 3位自增序号`
  - **程序部分**：例如`RPA_`。
  - **日期部分**：例如`20240501`。
  - **序号部分**：从`001`开始累加，最大至`999`，超过后无法继续创建。
- **示例**：
  - 任务名称：`RPA_20240501001`
- **并发处理**：
  - 在生成任务名称时，需要考虑并发情况，确保任务名称的唯一性。

#### 子任务名称的自动生成规则
- **格式**：`上级主任务名称 + '-' + 自增序号`
- **示例**：
  - 主任务名称：`RPA_20240501001`
  - 子任务1名称：`RPA_20240501001-1`
  - 子任务1的子任务名称：`RPA_20240501001-1-1`
  - 子任务2名称：`RPA_20240501001-2`

### 程序参数解析和保存规则

- **用途说明**：解析程序参数以方便用户填写参数，实际保存时以JSON格式保存。
- **页面排版**：
  - 每行显示：参数名称、参数值、参数描述。
  - 参数描述字体略小于参数名称，参数类型在前端不显示。
- **参数类型对应的输入控件**：
  - `string`：文本框。
  - `int`：数值输入框。
  - `bool`：复选框（`true`/`false`）。
  - `file`：文件上传控件（上传至文件存储功能，保存文件ID）。
    - 文件上传逻辑：点击上传控件时，调用文件管理上传接口，将文件上传至后端，上传成功返回ID值。"参数值"存储文件ID。接口：FileStorageController下的API接口
  - `select`：下拉选择控件（选项根据`ParametersSelectValue`字段）。
  - `RpaCredentials`：RPA账号凭证选择控件（选择RPA账号凭证）。

- **示例列表**：

  | 参数名称   | 参数描述          | 参数值（控件）                        |
  | ---------- | ----------------- | ------------------------------------- |
  | IsDebug    | 是否调试模式      | 复选框（`true`/`false`）              |
  | FileName   | 文件名            | 文本框                                |
  | InputFile  | 输入文件          | 文件上传控件（保存文件ID）            |
  | ChooseType | 选择类型，可多选  | 下拉选择控件（选项：`AAA`、`BBB`、`CCC`） |
  | Number     | 数量              | 数值输入框                            |
  | UserName | RPA账号凭证名称 | 选择RPA账号凭证控件,单选（选择RPA账号凭证） |

- **参数值保存**：
  - 用户填写的参数值，前端组装成JSON格式，保存到"程序输入参数"字段中。
  - **示例**：
    ```json
    {
      "IsDebug": "true",
      "FileName": "test.txt",
      "InputFile": "文件ID123456",
      "ChooseType": "AAA",
      "Number": "10", 
      "UserName": "RPA账号名称"
    }
    ```

- **特殊类型详细说明**：
  - **RPA账号凭证的选择类型**：
    - **用途**：用于选择RPA账号凭证。
    - **功能**：根据API接口获取所有RPA账号列表。
    - **前端显示**：单选下拉框，使用搜索单选下拉框组件选择。
    - **数据存储**：将选择的RPA账号凭证信息以字符串的格式存储。


### 指定资源机和资源池的字段说明：
    - **用途**：用于选择资源机或资源池。
    - **功能**：根据API接口获取所有资源机列表和资源池列表。
    - **前端显示**：多选下拉框，使用搜索多选下拉框组件选择，允许用户选择多个资源机和资源池，显示时可混合显示，但保存至后台时需要分开保存。
    - **数据存储**：将选择的资源机和资源池信息以字符串的格式存储在数据库字段中，中间用"|"分隔。
      - **字符串格式**：默认保存资源机名称，如："资源池名称1|资源池名称2|资源机名称1|资源机名称2"






### 任务类型说明

#### 普通任务（类型值：0）
- **描述**：根据优先级, 在资源机执行一个指定程序。

#### 编排任务（类型值：1）
- **描述**：作为父任务, 子任务可以是任何类型(普通任务,编排任务,系统编排拆分任务). 此类型的任务不会下发至资源机运行。
- **特性**：
  - 提供WebAPI接口供外部调用，可集成到第三方平台。
  - 子任务可以是: 普通任务,编排任务,系统编排拆分任务。
  - 

#### 系统编排拆分任务（类型值：2）
- **描述**：用于系统内部使用的小型编排任务，此类型的任务不会直接下发至资源机运行，但会自动生成多个子任务，这些子任务会下发至资源机进行执行。
- **特性**：
  - 系统编排任务创建后，将根据输入参数拆分Excel，并生成多个普通子任务，最后下发至资源机进行运行。
  - **拆分规则(以下"系统编排任务"="父任务")**：
    - **获取输入Excel文件**：从输入参数Json格式的`InputFile`字段中获取文件ID，并根据该ID在"文件管理表"中查询文件的二进制数据。
    - **Excel文件拆分流程**：按指定的每份拆分数量，将原有的Excel文件拆分为多个文件，并生同等数量的子任务。
      - 将Excel文件的第一个Sheet下的先去重，按每份拆分数量切分，生成多个Excel文件，每个Excel标题一样。
      - 生成的每个Excel文件存入文件管理，同时生成一条子任务, 任务类型=普通任务,其它字段与父任务相同. 将返回的文件ID覆盖到输入参数`InputFile`中(原始值是父任务的值). 注: 每个子任务的程序名称、程序参数（除了`InputFile`）、优先级等信息相同。
        - 例如：如果Excel文件有10行数据，且拆分量为2，则会生成5个子任务，每个子任务的输入Excel文件包含2行数据。
      - 所有子任务生成成功后将`父任务`的状态修改"Running"
  - **前端UI界面交互**：
    - 在新建任务界面时，当用户选择RPA程序类型时，会出现一个复选框：“系统拆分合并编排任务”。
    - 当勾选“系统拆分合并编排任务”时，复选框下方会出现2个额外的参数：
      1. **每份拆分数量**（数值文本框）：用于指定将Excel文件拆分的行数。
        - 点保存时存储方式：在"程序输入参数"的Json基础上添加"ExcelPerSplitNum"，值为文本框填写的值。如：{"InputFile":"1","ExcelPerSplitNum":"10"}
      2. **合并类型**（下拉框）：下拉可以为空，提供4种选项: 不覆盖文件、覆盖文件、不覆盖文件合并相同Excel、覆盖文件合并相同Excel。值为数字：0、1、2、3
        - 点保存时存储方式：在"程序输入参数"的Json基础上添加"MergeType"。如：{"InputFile":"1","ExcelPerSplitNum":"10","MergeType":"1"}
        - 默认不选择，无MergeType字段。如：{"InputFile":"1","ExcelPerSplitNum":"10"}
    - 当勾选“系统拆分合并编排任务”保存后, 需要检查输入参数里是否有"InputFile", 而且文件类型一定是".xlsx"
    - 新增任务界面排版与细节：
      - 程序列表 选择RPA类型时，才显示 任务类型 
      - 任务类型 在程序参数的下面。（也就是在界面的最下面。）
      - 任务类型的 是以 el-radio-button 单选的方式显示。单选只有两种类型： 普通任务、系统编排拆分任务
      - 当选择 系统编排拆分任务 时， 每份拆分数量与合并类型，在任务类型的下面
  
  - **任务状态处理**：
    - 系统编排任务完成后，根据参数执行合并功能。

  - **RPA合并功能**：
    - 根据合并类型执行合并功能。如果合并类型（MergeType）为空，则不执行合并功能。
    - 合并类型：0=不覆盖文件、1=覆盖文件、2=不覆盖文件合并相同Excel、3=覆盖文件合并相同Excel
    - 获取合并类型：从输入参数Json格式的`MergeType`字段中获取值。如下：
      ```json
      {
        {"ServerIP": "*************", "UserName": "aabb", "InputFile": "1045", "MergeType": "0", "ExcelPerSplitNum": "10"}
      }
      ```
    - 当满足合并条件时(服务IP（ServerIP）为空时抛异常)，创建合并任务：
      - 程序名称：RPASystem.MergeTool
      - 父任务ID：父任务ID
      - 资源机选择：ServerIP
      - 输入参数：
        - `targetPath`:  $@"\\{ServerIP}\VShare\WAutoDeploy\{程序名称}\OUTPUTRPASYSTEM\{TopJobTaskName}\{ParentTaskName}\{JobTaskName}"
        - `sourcePaths`: $@"\\{ServerIP}\VShare\WAutoDeploy\{程序名称}\OUTPUTRPASYSTEM\{TopJobTaskName}\{ParentTaskName}\{ParentTaskName}_[X]"
        - `processType`: MergeType

#### 针对于系统编排拆分任务的任态处理
- 父任务更新逻辑: 父任务下所有子任务状态=失败 或 取消 或 成功 时 更新父任务规则: 子任务其实中有一条: 失败=父任务失败,取消=父任务取消 , 子任务全成功=父任务成功. 任意子任务状态=等待 或 运行中 时, 父任务状态=运行中
- 如果在`父任务`拆分时失败：将错误信息写入输出参数里，并将状态设置为失败。
- 此规则只在`系统编排拆分任务`下所有子任务有效。


### 任务接口
#### 创建任意任务
- **接口描述**：创建一个任意任务类型。
- **请求方式**：`POST`
- **接口地址**：`/createOrcJobTask`
- **请求参数**：
  - `parentTaskID`：父任务ID（可选）。
  - `programName`：程序名称。
  - `inputParameters`：任务输入参数（JSON格式）。
  - `taskPriority`：任务优先级。
  - `TaskType`：任务类型。（普通任务=0、编排任务=1、系统编排拆分任务=2）
- **返回**：新创建的任务ID。

#### 获取编排任务状态
- **接口描述**：获取指定任务的当前状态。
- **请求方式**：`GET`
- **接口地址**：`/getOrcJobTaskStatus`
- **请求参数**：
  - `taskId`：任务ID。
- **返回**：(任务ID, 任务状态, 任务返回值)





---

## 附加功能

### 文件管理（文件存储功能）

**WEB后台功能**：

- **文件上传API接口**：提供文件上传的WebAPI接口，上传成功后返回文件ID。
- **文件信息获取**：根据文件ID获取文件信息，包括文件名、文件扩展名、文件数据等。
- **文件信息修改**：根据文件ID，可修改文件和备注等信息。

### 文件存储数据库字段

- **ID**
- **文件名**
- **文件扩展名**
- **文件数据**（二进制数据）
- **上传时间**
- **修改时间**（可选）
- **备注**（可选）

### Web服务初始化
- Web服务启动时，客户端状态全部重置为离线

### RPA的运行状态：异常、警告、已完成
- RPA的每个单号，运行后会有三种状态：异常、警告、已完成。一个单号结束后将信息写到输入的Excel的标题：RPA状态、PRA信息、RPA截图
- 将输入件没有列时，将自动创建Excel列，标题：RPA状态、PRA信息、RPA截图
- 第二次重跑时只会运行异常的和未完成的单号
#### 记录信息的Python方法：
```python
def ErrorInputLog(no, msg):
def WarningInputLog(no, msg):
def DoneInputLog(no):
# no: 单号, 根据单号找到指定行
# msg: 在指定行写入信息信息
```

### RPA任务重跑功能设计
- 将输入的Excel文件上传至后端替换原来的Excel文件，然后重新运行下发之前未运行完成或异常的单号


### 任务管理-输出文件
将程序运行输出的文件存放在共享目录，在任务管理列表点击可打开文件夹

### 定时任务管理
- 增删改查定时任务。
- 定时运行程序功能

### 独占运行
- 在资源机管理页面显示正在运行的数量
- 资源机运行任务数大于等于4（写死）个时，资源机状态设置为运行中。
- 程序是独占时，资源机状态设置为运行中。

### 配置表功能
- 可配置资源机最大同时运行数量
- 设置内存阈值重启功能。
- 设置任务输出文件存储服务器IP（支持多个，可以页面下拉选择）



### 程序管理-失败通知资源机
- 任务失败后，设置了失败通知的程序，失败后会通知指定设置的资源机

### 客户端-接受失败信息
- 在右下角弹出窗口，提示XXX任务在运行任务时失败！请及时处理。 点击打开链接：http://{ServerIP}:8887/RPA/JobTaskManager


### 录像功能
- 在RPA类型的任务运行时，自动录像在目录，与结果一并上传至服务端。
- 当任务开始时，开始录像，任务结束时停止录像。
- 只针对独享任务才录像，所以录像同一时间只有一个


### 资源机自动重启
- 当内存到达90%（可设置）时，将资源机状态设置为异常。当任务结束时。资源机将自动重启，并自动登陆。

### 任务自动重试
- 当前端页面点击重试时，重试次数+1
- 增加自动重试的定时任务
- 当资源机空闲时，自动重试失败的任务。
- 当优先级大于等于100时，不判断是否有空间机器，直接重试
- 当重试次数大于等于5次时，不再进行自动重试
- 只重试失败状态的任务

### 程序参数自动生成，上传自动填充参数


### 程序管理-存储方式
- 存储Zip压缩包时，有两种存储方式：1、本地存储 2、远程共享存储




### 任务列表-查询逻辑
1、没有查询条件时（所有查询条件为空时），查询Top 10 或Top N（前台查询决定）的顶级父任务，并查出所有父任务下的子任务。
2、有查询条件时，查出满足条件的Top N的任务，如果Top N里有顶级父任务，则顺便带出它底下的子任务。
3、查询默认倒序排序。