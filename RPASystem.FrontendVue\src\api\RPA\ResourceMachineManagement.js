import axios from 'axios'

/**
 * 获取资源机列表
 * @param {Object} params 查询参数
 * @param {number} params.pageIndex 当前页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.searchTerm 搜索关键字
 * @param {Array} params.taskStatusFilter 任务状态过滤
 * @param {boolean} params.offlineOverSevenDays 是否过滤离线超过7天的资源机
 * @returns {Promise} API响应，包含分页信息
 */
export function getResourceMachines(params) {
  // 创建新的参数对象，用于处理特殊的数组参数
  const queryParams = { ...params }
  
  // 特殊处理taskStatusFilter数组参数
  if (params.taskStatusFilter && params.taskStatusFilter.length > 0) {
    // axios会自动将数组参数转换为逗号分隔的字符串，这里使用paramsSerializer来保持数组格式
    return axios.get('/api/resourcemachine', { 
      params: queryParams,
      paramsSerializer: params => {
        const parts = [];
        for (const key in params) {
          if (Array.isArray(params[key])) {
            params[key].forEach(val => {
              parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(val)}`);
            });
          } else {
            parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
          }
        }
        return parts.join('&');
      }
    })
  }
  
  return axios.get('/api/resourcemachine', { params: queryParams })
}

/**
 * 删除资源机
 * @param {number} id 资源机ID
 * @returns {Promise} API响应
 */
export function deleteResourceMachine(id) {
  return axios.delete(`/api/resourcemachine/${id}`)
}

/**
 * 更新资源机类型
 * @param {number} id 资源机ID
 * @param {number} type 资源机类型 (0: 在线执行机, 1: 服务机, 2: 普通机)
 * @returns {Promise} API响应
 */
export function updateResourceMachineType(id, type) {
  return axios.put(`/api/resourcemachine/${id}/type`, type, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取最新客户端版本
 * @returns {Promise} API响应
 */
export function getLatestVersion() {
  return axios.get('/api/update/version')
}

/**
 * 上传新版本更新包
 * @param {FormData} formData 包含文件的表单数据
 * @returns {Promise} API响应
 */
export function uploadNewVersion(formData) {
  return axios.post('/api/update/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 