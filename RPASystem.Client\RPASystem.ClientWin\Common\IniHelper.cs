﻿using System;
using System.Runtime.InteropServices;
using System.Text;

public static class IniHelper
{
    [DllImport("kernel32", CharSet = CharSet.Unicode)]
    static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);

    [DllImport("kernel32", CharSet = CharSet.Unicode)]
    static extern long WritePrivateProfileString(string section, string key, string val, string filePath);

    private static readonly string IniFile = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.ini");

    public static string Read(string key, string section = "Root", string defaultValue = "")
    {
        var sb = new StringBuilder(255);
        GetPrivateProfileString(section, key, defaultValue, sb, sb.Capacity, IniFile);
        return sb.ToString();
    }

    public static void Write(string key, string value, string section = "Root")
    {
        WritePrivateProfileString(section, key, value, IniFile);
    }
}
