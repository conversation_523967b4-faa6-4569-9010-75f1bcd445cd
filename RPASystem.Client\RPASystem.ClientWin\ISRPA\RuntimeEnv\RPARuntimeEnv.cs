using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading;
using System.Collections.Generic;
using System.Windows.Documents;
using System.Diagnostics;

namespace RPASystem.ClientWin.ISRPA.RuntimeEnv
{
    /// <summary>
    /// 插件运行环境更新类
    /// </summary>
    public static class RPARuntimeEnv
    {

        static string RPAInstallPath = ISRPAUtils.GetInstallPath();

        /// <summary>
        /// 插件配置字典，key是RuntimeEnv目录下的目录名，value是目标路径
        /// </summary>
        private static readonly Dictionary<string, string> pluginPathMapping = new Dictionary<string, string>
        {
            { "Plugin", @"C:\iS-RPA\push\Plugin" },
            { "PublicComponents2022", Path.Combine(RPAInstallPath,"Python\\Lib\\ubpalib")},
            // 可以在这里添加更多插件的路径映射
            // { "Plugin3", @"E:\SomeOtherPath\Plugin3" },
            // { "Plugin4", @"F:\AnotherPath\Plugin4" }
        };

        /// <summary>
        /// 如果安装了RPA，则更新所有插件和组件
        /// </summary>
        public static void Update()
        {
            if (string.IsNullOrEmpty(RPAInstallPath)) return;
            foreach (var pluginName in pluginPathMapping.Keys)
            {
                UpdatePlugin(pluginName);
            }
        }

        /// <summary>
        /// 更新指定名称的插件
        /// </summary>
        /// <param name="pluginName">插件名称</param>
        public static void UpdatePlugin(string pluginName)
        {
            try
            {
                // 检查插件是否在配置中
                if (!pluginPathMapping.ContainsKey(pluginName))
                {
                    Console.WriteLine($"插件 {pluginName} 未在配置中定义，无法更新");
                    return;
                }

                // 源目录（zip包所在位置）
                string sourceDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ISRPA", "RuntimeEnv", pluginName);

                // 目标目录（更新解压位置）
                string targetDir = pluginPathMapping[pluginName];

                // 源目录不存在或目标目录为空
                if (!Directory.Exists(sourceDir) || string.IsNullOrEmpty(targetDir))
                {
                    return;
                }

                // 版本文件路径
                string versionFilePath = Path.Combine(targetDir, "Version.txt");

                // 确保目标目录存在
                if (!Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                // 获取最新版本的ZIP包
                string latestZipFile = GetLatestVersionZip(sourceDir);
                if (string.IsNullOrEmpty(latestZipFile))
                {
                    Console.WriteLine($"没有找到{pluginName}的更新包");
                    return;
                }

                // 从文件名中提取版本号
                string newVersion = Path.GetFileNameWithoutExtension(latestZipFile);

                // 获取当前版本号
                string currentVersion = GetCurrentVersion(versionFilePath);

                // 比较版本号，如果相同则不更新
                if (string.Equals(newVersion, currentVersion))
                {
                    Console.WriteLine($"{pluginName}已经是最新版本：{currentVersion}");
                    return;
                }

                Console.WriteLine($"开始更新{pluginName}，从版本{currentVersion}到{newVersion}");

                // 清空目标目录
                DeleteDirectoryContents(targetDir);

                // 解压新版本到目标目录
                ZipFile.ExtractToDirectory(Path.Combine(sourceDir, latestZipFile), targetDir);

                // 更新版本号文件
                File.WriteAllText(versionFilePath, newVersion);

                Console.WriteLine($"{pluginName}更新完成，当前版本：{newVersion}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新{pluginName}时出错：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取目录中最新版本的ZIP文件
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <returns>最新版本ZIP文件名</returns>
        private static string GetLatestVersionZip(string directory)
        {
            if (!Directory.Exists(directory))
            {
                return null;
            }

            var zipFiles = Directory.GetFiles(directory, "*.zip")
                .Select(f => new
                {
                    FullPath = f,
                    FileName = Path.GetFileNameWithoutExtension(f),
                    Version = ParseVersion(Path.GetFileNameWithoutExtension(f))
                })
                .Where(f => f.Version != null)
                .OrderByDescending(f => f.Version)
                .ToList();

            return zipFiles.Any() ? Path.GetFileName(zipFiles.First().FullPath) : null;
        }

        /// <summary>
        /// 解析版本号
        /// </summary>
        /// <param name="versionString">版本号字符串</param>
        /// <returns>版本号对象</returns>
        private static Version ParseVersion(string versionString)
        {
            try
            {
                return new Version(versionString);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取当前版本号
        /// </summary>
        /// <param name="versionFilePath">版本文件路径</param>
        /// <returns>当前版本号</returns>
        private static string GetCurrentVersion(string versionFilePath)
        {
            if (!File.Exists(versionFilePath))
            {
                return string.Empty;
            }

            string[] lines = File.ReadAllLines(versionFilePath);
            return lines.Length > 0 ? lines[0].Trim() : string.Empty;
        }

        /// <summary>
        /// 清空目录内容（使用Windows命令）
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        private static void DeleteDirectoryContents(string directoryPath)
        {
            if (!Directory.Exists(directoryPath)) return;

            DateTime startTime = DateTime.Now;
            TimeSpan maxRetryTime = TimeSpan.FromHours(1);
            bool isDeleted = false;

            try
            {
                while (!isDeleted)
                {
                    // 使用Windows命令删除目录内容
                    Process process = new Process();
                    process.StartInfo.FileName = "cmd.exe";
                    process.StartInfo.Arguments = $"/c rd /s /q \"{directoryPath}\"";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();
                    process.WaitForExit();

                    // 检查是否删除成功
                    if (!Directory.Exists(directoryPath) || 
                        (Directory.GetFiles(directoryPath).Length == 0 && Directory.GetDirectories(directoryPath).Length == 0))
                    {
                        isDeleted = true;
                    }
                    else
                    {
                        // 检查是否超时
                        if (DateTime.Now - startTime > maxRetryTime)
                        {
                            throw new TimeoutException($"无法删除目录内容 {directoryPath}，超过1小时重试时间。");
                        }
                        
                        // 等待1秒后重试
                        Thread.Sleep(1000);
                    }
                }

                // 确保目录存在（如果被完全删除了）
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除目录内容时出错：{ex.Message}");
                throw;
            }
        }
    }
}