using RPASystem.Model;

namespace RPASystem.Service
{
    public interface IOrchestrationService
    {
        Task<long> CreateOrchestrationTask(OrchestrationTaskDto dto);
        Task ProcessSplitTaskAsync(JobTask jobTask);
        Task<OrchestrationTaskStatusDto> GetOrchestrationTaskStatus(long taskId);
        Task<OrchestrationTaskStatusDto> GetOrchestrationTaskStatusByName(string taskName);
        Task<Dictionary<string, OrchestrationTaskStatusDto>> GetOrchestrationTaskStatusByNames(string[] taskNames);
    }
} 