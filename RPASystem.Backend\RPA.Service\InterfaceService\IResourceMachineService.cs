using RPASystem.Model;


namespace RPASystem.Service
{
    public interface IResourceMachineService
    {
        Task RegisterOrUpdateMachineAsync(string machineName, string clientVersion = null, bool isLatestVersion = false);
        Task UpdateMachineStatusAsync(string machineName, TaskStatusEnum status, List<long> runningTaskIds = null);
        Task<List<ResourceMachine>> GetIdleAsync();
        Task<List<ResourceMachine>> GetAllResourceMachinesAsync();
        Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null);
        Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null, bool? offlineOverSevenDays = null);
        Task<(List<ResourceMachine> Items, int Total)> GetResourceMachinesAsync(int pageIndex, int pageSize, string? searchTerm = null, bool? offlineOverSevenDays = null, List<string>? taskStatusFilter = null);
        Task UpdateResourceInfoAsync(ResourceInfoDto resourceInfo);
        Task<bool> DeleteResourceMachineAsync(long id);
        Task InitializeResourceMachineStatusAsync();
        /// <summary>
        /// 更新资源机任务状态
        /// </summary>
        /// <param name="machineName">机器名</param>
        /// <param name="runningTaskIds">任务IDS</param>
        /// <param name="hasExclusiveTask">是否有独占任务</param>
        /// <returns></returns>
        Task UpdateResourceMachineTaskStatusAsync(string machineName, List<long> runningTaskIds, bool hasExclusiveTask = false);
        Task SetAllOnlineMachinesToRunningAsync();
        Task SetAllMachinesToNonLatestVersionAsync();
        Task UpdateMachineVersionStatusAsync(string latestVersion);
        Task<bool> UpdateMachineTypeAsync(long id, MachineTypeEnum machineType);

        Task<ResourceMachine> GetResourceMachineAsync(string machineName);
    }
}