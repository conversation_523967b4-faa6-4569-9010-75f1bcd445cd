﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace RPASystem.MergeTool
{
    internal class Options : ParametersModelBase
    {
        //[DisplayName('t', "targetPath", Required = true, HelpText = "要合并的目标文件夹")]
        [InputParamUseKey(DisplayName = "要合并的目标文件夹", Required = true)]
        public string TargetPath { get; set; }

        //[Option('s', "sourcePaths", Required = true, HelpText = "要合并的源文件夹")]
        [InputParamUseKey(DisplayName = "要合并的源文件夹", Required = true)]
        public string SourcePaths { get; set; }

        //[Option('p', "processType", Required = false, Default = "1", HelpText = "处理方式：0-不覆盖不合并，1-覆盖不合并，2-不覆盖合并Excel，3-覆盖合并Excel")]
        [InputParamUseKey(DisplayName = "处理方式：0-不覆盖不合并，1-覆盖不合并，2-不覆盖合并Excel，3-覆盖合并Excel")]
        public string ProcessType { get; set; }
    }
}
