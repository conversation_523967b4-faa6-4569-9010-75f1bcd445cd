<template>
  <div>
    <div v-for="(param, index) in parameters" :key="index" class="param-row">
      <el-select v-model="param.ParametersType" style="width: 120px;">
        <el-option label="字符串" value="string"></el-option>
        <el-option label="整数" value="int"></el-option>
        <el-option label="布尔值" value="bool"></el-option>
        <el-option label="文件" value="file"></el-option>
        <el-option label="选择" value="select"></el-option>
        <el-option label="资源选择" value="ResourceSelection"></el-option>
        <el-option label="RPA账号凭证" value="RpaCredentials"></el-option>
      </el-select>
      <el-input v-model="param.ParametersName" placeholder="参数名称" style="width: 150px; margin: 0 10px;"></el-input>
      <el-input v-model="param.ParametersDescription" placeholder="参数描述" style="width: 150px; margin-right: 10px;"></el-input>
      <template v-if="param.ParametersType === 'RpaCredentials'">
        <el-input v-model="param.DefaultValue" placeholder="默认账号名称" style="width: 120px; margin-right: 10px;"></el-input>
      </template>
      <template v-else-if="param.ParametersType !== 'select' && param.ParametersType !== 'bool'">
        <el-input v-model="param.DefaultValue" placeholder="默认值" style="width: 120px; margin-right: 10px;"></el-input>
      </template>
      <template v-else-if="param.ParametersType === 'bool'">
        <el-select v-model="param.DefaultValue" style="width: 120px; margin-right: 10px;">
          <el-option label="是" value="true"></el-option>
          <el-option label="否" value="false"></el-option>
        </el-select>
      </template>
      <template v-if="param.ParametersType === 'select'">
        <el-input v-model="param.ParametersSelectValue" placeholder="选项值(用|分隔)" style="width: 200px; margin-right: 10px;"></el-input>
        <el-select v-model="param.ParametersOptions" style="width: 100px; margin-right: 10px;">
          <el-option label="单选" value="1"></el-option>
          <el-option label="多选" value="2"></el-option>
        </el-select>
        <el-input v-model="param.DefaultValue" placeholder="默认值" style="width: 120px; margin-right: 10px;"></el-input>
      </template>
      <el-switch v-model="param.IsRequired" active-text="必填" inactive-text="选填" style="margin-right: 10px;"></el-switch>
      <el-button type="danger" @click="removeParameter(index)">删除</el-button>
    </div>
    <el-button type="primary" @click="addParameter" style="margin-top: 10px;">添加参数</el-button>
  </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
  props: {
    programType: {
      type: Number,
      required: true
    },
    initialParameters: {
      type: String,
      default: '[]'
    }
  },
  emits: ['update:parameters'],
  setup(props, { emit }) {
    const parameters = ref([]);

    const initializeParameters = () => {
      try {
        // 确保initialParameters是有效的JSON字符串
        if (!props.initialParameters || props.initialParameters.trim() === '') {
          // 如果为空，根据程序类型设置默认参数
          if (props.programType === 0) { // RPA
            parameters.value = [
              { 
                ParametersName: 'InputFile', 
                ParametersType: 'file', 
                ParametersDescription: '输入文件',
                DefaultValue: '',
                IsRequired: true 
              },
              { 
                ParametersName: 'ServerIP', 
                ParametersType: 'string', 
                ParametersDescription: '服务器IP',
                DefaultValue: '',
                IsRequired: true 
              },
              { 
                ParametersName: 'UserName', 
                ParametersType: 'RpaCredentials', 
                ParametersDescription: '用户名',
                DefaultValue: '',
                IsRequired: true 
              }
            ];
          } else {
            parameters.value = [];
          }
          return;
        }

        const parsedParams = JSON.parse(props.initialParameters);
        if (Array.isArray(parsedParams) && parsedParams.length > 0) {
          parameters.value = parsedParams;
        } else if (props.programType === 0) { // RPA
          parameters.value = [
            { 
              ParametersName: 'InputFile', 
              ParametersType: 'file', 
              ParametersDescription: '输入文件',
              DefaultValue: '',
              IsRequired: true 
            },
            { 
              ParametersName: 'ServerIP', 
              ParametersType: 'string', 
              ParametersDescription: '服务器IP',
              DefaultValue: '',
              IsRequired: true 
            },
            { 
              ParametersName: 'UserName', 
              ParametersType: 'RpaCredentials', 
              ParametersDescription: '用户名',
              DefaultValue: '',
              IsRequired: true 
            }
          ];
        } else { // EXE或其他类型
          parameters.value = [];
        }
      } catch (error) {
        console.error('解析参数失败:', error);
        // 错误处理：根据程序类型设置默认参数
        if (props.programType === 0) { // RPA
          parameters.value = [
            { 
              ParametersName: 'InputFile', 
              ParametersType: 'file', 
              ParametersDescription: '输入文件',
              DefaultValue: '',
              IsRequired: true 
            },
            { 
              ParametersName: 'ServerIP', 
              ParametersType: 'string', 
              ParametersDescription: '服务器IP',
              DefaultValue: '',
              IsRequired: true 
            },
            { 
              ParametersName: 'UserName', 
              ParametersType: 'RpaCredentials', 
              ParametersDescription: '用户名',
              DefaultValue: '',
              IsRequired: true 
            }
          ];
        } else {
          parameters.value = [];
        }
      }
    };

    const addParameter = () => {
      parameters.value.push({
        ParametersName: '',
        ParametersType: 'string',
        ParametersDescription: '',
        DefaultValue: '',
        IsRequired: true,
        ParametersOptions: '1',
        ParametersSelectValue: ''
      });
      emitUpdate();
    };

    const removeParameter = (index) => {
      parameters.value.splice(index, 1);
      emitUpdate();
    };

    const emitUpdate = () => {
      emit('update:parameters', JSON.stringify(parameters.value));
    };

    const validateParameters = (parameters) => {
      return parameters.every(param => {
        // 基本验证
        if (!param.ParametersName || !param.ParametersType) {
          return false;
        }

        // 特殊类型验证
        switch (param.ParametersType) {
          case 'ResourceSelection':
            return true; // 资源选择不需要额外验证
          case 'RpaCredentials':
            return true; // RPA凭证不需要额外验证
          case 'select':
            return !!param.ParametersSelectValue; // select类型需要有选项值
          default:
            return true;
        }
      });
    };

    watch(() => props.initialParameters, initializeParameters, { immediate: true });
    watch(() => props.programType, initializeParameters);
    watch(parameters, emitUpdate, { deep: true });

    return {
      parameters,
      addParameter,
      removeParameter
    };
  }
};
</script>

<style scoped>
.param-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
