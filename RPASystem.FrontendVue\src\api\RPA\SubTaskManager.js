import axios from 'axios'

// 获取子任务列表
export function getSubTasks(params) {
  return axios.get('/api/jobtask/subtasks', { params })
}

// 重试子任务
export function retrySubTask(jobTaskId) {
  return axios.post(`/api/jobtask/retry/${jobTaskId}`)
}

// 停止子任务
export function stopSubTask(jobTaskId) {
  return axios.post(`/api/jobtask/stop/${jobTaskId}`)
}

// 下载输入文件
export function downloadInputFile(fileId) {
  return axios.get(`/api/filestorage/${fileId}`, { 
    responseType: 'blob',
    headers: { 'Accept': 'application/octet-stream' }
  })
} 