@echo off
chcp 65001
REM 设置目标目录
set TARGET_DIR=Z:\VV35\RPASystem.FrontendVueV3-Publish\html

echo 删除目标目录
rd /S /Q "%TARGET_DIR%"

echo 开始构建前端项目...
echo.

REM 运行 npm build 命令并捕获错误
call npm run build:prod
if errorlevel 1 (
    echo 构建失败，请检查错误信息
    pause
    exit /b 1
)

echo 构建成功，开始复制文件...
echo.

REM 检查目标目录是否存在，如果不存在则创建
if not exist "%TARGET_DIR%" mkdir "%TARGET_DIR%"

REM 复制 dist 目录下的所有文件和文件夹到目标目录
xcopy /E /I /Y dist\* "%TARGET_DIR%"

echo.
echo 文件已成功复制到 %TARGET_DIR%
echo.
echo 按任意键退出...
pause