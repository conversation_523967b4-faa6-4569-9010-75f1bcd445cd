﻿using Microsoft.AspNetCore.SignalR;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.WebApi.Hubs;
using RPASystem.Model;
using System.Text.Json;
using System.Xml.Linq;
using Infrastructure;

public class ScheduledTask : IHostedService, IDisposable
{
    private readonly ILogger<ScheduledTask> logger;
    private readonly IServiceProvider serviceProvider;
    private Timer timer;
    private Timer timerAutoRetry; // 添加自动重试定时器
    private IHubContext<ResourceMachineHub> resourceMachineHubContext;
    private static readonly object taskLock = new object();
    private static int isRunningFlag = 0;
    private static int isRetryRunningFlag = 0; // 自动重试运行标志
    //private static int isRunningFlag1 = 0;

    public ScheduledTask(ILogger<ScheduledTask> logger, IServiceProvider serviceProvider, IHubContext<ResourceMachineHub> resourceMachineHubContext)
    {
        this.logger = logger;
        this.serviceProvider = serviceProvider;
        this.resourceMachineHubContext = resourceMachineHubContext;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Scheduled Task is starting.");
        timer = new Timer(GetTaskAndPush, null, TimeSpan.Zero, TimeSpan.FromSeconds(4));
        timerAutoRetry = new Timer(AutoRetryFailedTasks, null, TimeSpan.FromSeconds(600), TimeSpan.FromSeconds(600)); // 每10分钟执行一次自动重试
        return Task.CompletedTask;
    }

    private async void GetTaskAndPush(object state)
    {
        if (Interlocked.CompareExchange(ref isRunningFlag, 1, 0) != 0)
        {
            logger.LogInformation("上一次任务还在执行中，跳过本次执行");
            return;
        }

        try
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
                var resourceMachineService = scope.ServiceProvider.GetRequiredService<IResourceMachineService>();

                // 1. 获取空闲资源机
                var idleResourceMachines = await resourceMachineService.GetIdleAsync();
                if (idleResourceMachines.Count == 0)
                {
                    return;
                }

                // 2. 获取待执行任务
                var pendingTasks = await jobTaskService.GetPendingTasksWithProgramAsync();
                if (pendingTasks.Count == 0)
                {
                    return;
                }

                // 3. 为每个任务找到合适的资源机
                //var dispatchTasks = new List<Task>();
                foreach (var task in pendingTasks)
                {
                    ResourceMachine selectedMachine = null;




                    if (!string.IsNullOrEmpty(task.ResourceSelection))
                    {
                        // 获取指定的可用资源机列表
                        var availableMachines = await jobTaskService.GetAvailableResourceMachinesAsync(task.ResourceSelection);
                        // 在空闲资源机中查找指定的资源机
                        selectedMachine = idleResourceMachines.FirstOrDefault(m => availableMachines.Contains(m.MachineName));
                    }
                    else
                    {
                        // 如果没有指定资源机，只能使用在线执行机
                        selectedMachine = idleResourceMachines.FirstOrDefault(m => m.MachineType == MachineTypeEnum.在线执行机);
                    }

                    // 特殊服务机场景，如果资源机不为空（重跑）且是服务机，只能在本机上执行
                    if (!string.IsNullOrWhiteSpace(task.AssignedResourceMachine))
                    {
                        // 查是否为服务机
                        // 优化, 空闲有,且是服务机,直接分配
                        var machineIdle = idleResourceMachines.FirstOrDefault(t => t.MachineName == task.AssignedResourceMachine && t.MachineType == MachineTypeEnum.服务机);
                        if (machineIdle != null)
                        {
                            selectedMachine = machineIdle;
                        }
                        else
                        {
                            // 空闲没有,且是服务机
                            var machine = await resourceMachineService.GetResourceMachineAsync(task.AssignedResourceMachine);
                            // 是服务机则忽略任务
                            if (machine != null && machine.MachineType == MachineTypeEnum.服务机)
                            {
                                continue;
                            }
                        }
                    }


                    // 最终匹配到的机器
                    if (selectedMachine != null)
                    {
                        if (ResourceMachineHub.machineConnections.TryGetValue(selectedMachine.MachineName, out string connectionId))
                        {
                            // 只有是独占程序和任务数大于等于4时，更新任务状态
                            await resourceMachineService.UpdateMachineStatusAsync(selectedMachine.MachineName, TaskStatusEnum.任务运行中);
                            // 移出List的空闲资源机
                            idleResourceMachines.Remove(selectedMachine);
                            // 分发任务
                            var ret = await DispatchTaskToMachineAsync(task, selectedMachine, connectionId, jobTaskService);
                            // 如果发送失败，一律将资源机设置为离线
                            if (!ret)
                            {
                                await resourceMachineService.UpdateMachineStatusAsync(selectedMachine.MachineName, TaskStatusEnum.离线);
                            }
                            //// 使用Task.Run创建新线程资源任务分发
                            //var dispatchTask = Task.Run(() => DispatchTaskToMachineAsync(task, selectedMachine, connectionId, jobTaskService));
                            //dispatchTasks.Add(dispatchTask);
                        }
                        else
                        {
                            await resourceMachineService.UpdateMachineStatusAsync(selectedMachine.MachineName, TaskStatusEnum.离线);
                            logger.LogWarning($"资源机 {selectedMachine.MachineName} 不在连接字典中，可能已离线");
                        }
                    }
                }
                await HubMessageSender.RefreshJobTasks();
            }
        }
        catch (Exception ex)
        {
            logger.LogError("GetTaskAndPush执行异常：" + ex.ToString());
        }
        finally
        {
            Interlocked.Exchange(ref isRunningFlag, 0);
        }
    }

    // 自动重试失败任务
    private async void AutoRetryFailedTasks(object state)
    {
        if (Interlocked.CompareExchange(ref isRetryRunningFlag, 1, 0) != 0)
        {
            logger.LogInformation("上一次自动重试任务还在执行中，跳过本次执行");
            return;
        }

        try
        {
            //logger.LogInformation("开始执行自动重试失败任务");
            using (var scope = serviceProvider.CreateScope())
            {
                var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
                var resourceMachineService = scope.ServiceProvider.GetRequiredService<IResourceMachineService>();
                
                // 获取需要重试的任务列表
                var failedTasks = await jobTaskService.GetTasksForAutoRetryAsync();
                
                if (failedTasks.Count == 0)
                {
                    //logger.LogInformation("没有需要自动重试的失败任务");
                    return;
                }

                //logger.LogInformation($"找到 {failedTasks.Count} 个需要自动重试的失败任务");
                
                // 获取空闲资源机
                var idleResourceMachines = await resourceMachineService.GetIdleAsync();
                bool haveRetry = false;
                // 遍历失败任务执行重试
                foreach (var task in failedTasks)
                {
                    try
                    {
                        // 优先级大于等于1000的任务不考虑资源机状态，直接重试
                        bool shouldRetry = task.Priority >= 1000;
                        
                        // 如果优先级低于100，则需要检查是否有空闲资源机
                        if (!shouldRetry && idleResourceMachines.Count > 0)
                        {
                            // 检查是否有匹配的资源机可用
                            if (string.IsNullOrEmpty(task.ResourceSelection))
                            {
                                // 无指定资源机的任务只需要有在线执行机即可
                                shouldRetry = idleResourceMachines.Any(m => m.MachineType == MachineTypeEnum.在线执行机);
                            }
                            else
                            {
                                // 获取指定的可用资源机列表
                                var availableMachines = await jobTaskService.GetAvailableResourceMachinesAsync(task.ResourceSelection);
                                shouldRetry = idleResourceMachines.Any(m => availableMachines.Contains(m.MachineName));
                            }
                        }

                        if (shouldRetry)
                        {
                            // 调用服务层的重试方法
                            await jobTaskService.RetryJobTaskAsync(task.JobTaskId);
                            //logger.LogInformation($"自动重试任务 {task.JobTaskName}（ID: {task.JobTaskId}）成功");
                            haveRetry = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"自动重试任务 {task.JobTaskName}（ID: {task.JobTaskId}）时发生异常: {ex.Message}");
                    }
                }
                // 有重试时才，刷新前端页面
                if (haveRetry)
                {
                    await HubMessageSender.RefreshJobTasks();
                }
                

            }
        }
        catch (Exception ex)
        {
            logger.LogError("AutoRetryFailedTasks执行异常：" + ex.ToString());
        }
        finally
        {
            Interlocked.Exchange(ref isRetryRunningFlag, 0);
        }
    }

    private async Task<bool> DispatchTaskToMachineAsync(ViewJobTaskExeProgram task, ResourceMachine selectedMachine, string connectionId, IJobTaskService jobTaskService)
    {
        try
        {
            // 1. 获取顶层任务名称
            string topTaskName = string.Empty;
            if (task.ParentTaskID != 0) { topTaskName = await jobTaskService.GetTopTaskNameAsync(task.JobTaskId); }

            // 2. 构建任务模型
            var jobModel = new JobModel
            {
                Id = task.JobTaskId,
                JobTaskName = task.JobTaskName,
                TopJobTaskName = topTaskName,
                ProgramType = task.ProgramType,
                Parameter = task.InputParameters,
                ProgramName = task.ExeProgramName,
                ProgramVersion = task.Version,
                IsExclusive = task.IsExclusive,
                OutputFile = task.OutputFile
            };

            // 添加超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            try
            {
                bool received = await resourceMachineHubContext.Clients.Client(connectionId).InvokeAsync<bool>("RunTask", jobModel, cts.Token);
                if (received)
                {
                    await jobTaskService.UpdateJobTaskStatusAsync(jobModel.Id, JobTaskStatusEnum.Running, selectedMachine.MachineName);
                    return true;
                }
            }
            catch (OperationCanceledException ex)
            {
                logger.LogError($"向资源机 {selectedMachine.MachineName} 发送任务超时，异常：" + ex.Message);
                // await jobTaskService.UpdateTaskWithErrorAsync(jobModel.Id, "任务分发超时");
            }
        }
        catch (Exception ex)
        {
            logger.LogError($"向资源机 {selectedMachine.MachineName} 发送任务异常：" + ex.ToString());
        }
        return false;
    }
    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Scheduled Task is stopping.");
        timer?.Change(Timeout.Infinite, 0);
        timerAutoRetry?.Change(Timeout.Infinite, 0); // 停止自动重试定时器
        return Task.CompletedTask;
    }


    public void Dispose()
    {
        timer?.Dispose();
        timerAutoRetry?.Dispose(); // 释放自动重试定时器资源
    }
}
