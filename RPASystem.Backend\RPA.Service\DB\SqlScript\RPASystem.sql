-- 创建ResourceMachines表
CREATE TABLE ResourceMachines (
    -- 主键ID，自动递增
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    -- 资源机名称，唯一标识资源机
    MachineName VARCHAR(255) NOT NULL UNIQUE,
    -- 资源机的计算机名
    ComputerName VARCHAR(255),
    -- 最后活动时间
    LastActivityTime DATETIME NOT NULL,
    -- 当前运行的EXE程序名称，支持多个用|隔开，空闲时为空
    RunningExeNames TEXT,
    -- 资源机运行状态（0: 空闲, 1: 任务运行中, 2: 离线）
    TaskStatus ENUM('空闲', '任务运行中', '离线') NOT NULL DEFAULT '空闲'  COMMENT '资源机运行状态',
    -- CPU使用情况，百分比
    CpuUsage FLOAT,
    -- 内存使用情况，百分比
    MemoryUsage FLOAT,
    -- 磁盘使用情况，百分比
    DiskUsage VARCHAR(1000),
    -- 资源机类型
    MachineType ENUM('在线执行机', '服务机', '普通机') NOT NULL DEFAULT '在线执行机' COMMENT '资源机类型',
    -- 主机IP地址
    IpAddress VARCHAR(50) NULL COMMENT '主机IP地址',
    -- 客户端版本
    ClientVersion VARCHAR(50) NULL COMMENT '客户端版本',
    -- 是否最新版本
    IsLatestVersion TINYINT(1) NOT NULL DEFAULT '0',
    -- 运行任务数
    RunningTaskCount TINYINT NOT NULL DEFAULT '0' COMMENT '运行任务数'
);
-- 创建索引以加快查询速度（如果有需要，可以添加更多索引）
CREATE INDEX idx_LastActivityTime ON ResourceMachines (LastActivityTime);
CREATE INDEX idx_RunningTaskCount ON ResourceMachines (RunningTaskCount);





-- 创建EXE程序表
CREATE TABLE ExePrograms (
    ID BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '程序ID',
    ProgramName VARCHAR(255) NOT NULL COMMENT '程序名称',
    Version VARCHAR(50) COMMENT '版本号',
    InputParameters JSON COMMENT '程序输入参数（JSON格式）',
    IsExclusive BOOLEAN DEFAULT FALSE COMMENT '是否独占资源机',
    ProgramPackage VARCHAR(255) COMMENT '程序包文件路径',
    ProgramType ENUM('RPA', 'EXE') NOT NULL DEFAULT 'RPA'  COMMENT '程序类型（RPA或EXE）',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    Remarks TEXT COMMENT '备注',
    ResourceSelection TEXT COMMENT '指定资源机和资源池运行，多个用|分隔,默认保存'
) COMMENT='EXE程序管理表';

-- 添加程序名唯一索引
CREATE UNIQUE INDEX idx_ProgramName ON ExePrograms(ProgramName);


-- 创建任务表
CREATE TABLE JobTasks (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID',
    ParentTaskID BIGINT NOT NULL DEFAULT 0 COMMENT '父任务ID',
    TaskType ENUM('Normal', 'Orchestration', 'SystemOrchestrationSplit') NOT NULL DEFAULT 'Normal' COMMENT '任务类型',
    ExeProgramID BIGINT NOT NULL COMMENT '程序ID',
    TaskName VARCHAR(255) COMMENT '任务名称',
    Priority INT NOT NULL DEFAULT 0 COMMENT '任务优先级',
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务创建时间',
    AssignedResourceMachine VARCHAR(255) COMMENT '任务分配的资源机名称',
    InputParameters JSON COMMENT '程序输入参数（JSON）',
    OutputResults TEXT COMMENT '程序输出结果',
    Status ENUM('Pending', 'Running', 'Success', 'Failed', 'Cancelled') NOT NULL DEFAULT 'Pending' COMMENT '任务状态',
    StartTime DATETIME COMMENT '运行开始时间',
    EndTime DATETIME COMMENT '运行结束时间',
    Notes TEXT COMMENT '备注',
    ResourceSelection TEXT COMMENT '指定资源机和资源池运行，多个用|分隔',
    OutputFile TEXT COMMENT '程序输出文件',
    RetryCount SMALLINT NOT NULL DEFAULT 0 COMMENT '重试次数'
);


-- 创建FileStorage表
CREATE TABLE IF NOT EXISTS FileStorages (
    ID bigint AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    FileName varchar(255) NOT NULL COMMENT '文件名',
    FileExtension varchar(50) NOT NULL COMMENT '文件扩展名',
    FileData longblob NOT NULL COMMENT '文件数据(二进制)',
    UploadTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    ModifyTime DATETIME COMMENT '修改时间',
    Remark VARCHAR(500) COMMENT '备注'
);


-- 创建ResourcePools表
create table ResourcePools (
    ID bigint auto_increment primary key comment '资源池ID',
    PoolName varchar(255) not null comment '资源池名称',
    Description varchar(500) comment '资源池描述',
    ResourceMachineNames text comment '资源机名称列表，用|分隔',
    CreatedTime datetime not null default current_timestamp comment '创建时间',
    UpdatedTime datetime on update current_timestamp comment '更新时间'
) comment='资源池表';

-- 创建索引
create index idx_PoolName on ResourcePools(PoolName);


-- 创建RPA账号凭证表
create table RpaCredentials (
    ID bigint auto_increment primary key comment '凭证ID',
    Username varchar(100) not null comment '用户名',
    Password varchar(255) not null comment '密文',
    Description varchar(500) comment '描述',
    CreatedTime datetime not null default current_timestamp comment '创建时间',
    UpdatedTime datetime on update current_timestamp comment '更新时间'
) comment='RPA账号凭证表';

create index idx_CredentialName on RpaCredentials(Username);



-- 以下为版本所有更新

-- 更新IsLatestVersion字段的脚本
-- ALTER TABLE ResourceMachines ADD IsLatestVersion TINYINT(1) NOT NULL DEFAULT '0';
-- alter table ExePrograms modify COLUMN ProgramPackage VARCHAR(255) COMMENT '程序包文件路径';

-- 添加RunningTaskCount字段的升级脚本
-- ALTER TABLE ResourceMachines ADD COLUMN RunningTaskCount TINYINT NOT NULL DEFAULT '0' COMMENT '运行任务数';
-- CREATE INDEX idx_RunningTaskCount ON ResourceMachines (RunningTaskCount);

-- 向JobTasks表添加新字段的升级脚本
-- ALTER TABLE JobTasks ADD COLUMN OutputFile TEXT COMMENT '程序输出文件';
-- ALTER TABLE JobTasks ADD COLUMN RetryCount SMALLINT NOT NULL DEFAULT 0 COMMENT '重试次数';



-- 创建系统配置表
create table if not exists SystemConfig (
    ID bigint primary key auto_increment,
    ConfigKey varchar(100) not null,
    ConfigValue varchar(500) not null,
    Description varchar(500),
    ConfigGroup varchar(50),
    IsSystem tinyint(1) not null default 0,
    CreatedTime datetime not null default current_timestamp,
    UpdatedTime datetime,
    unique key idx_config_key (ConfigKey)
) engine=innodb default charset=utf8mb4;

-- 插入默认配置
insert into SystemConfig (ConfigKey, ConfigValue, Description, ConfigGroup, IsSystem, CreatedTime)
values 
('ResourceMachine.MaxConcurrentTasks', '3', '资源机最大同时运行任务数量', 'ResourceMachine', 1, now()),
('ResourceMachine.MemoryThreshold', '90', '内存使用率阈值（百分比），超过此值将触发重启', 'ResourceMachine', 1, now()),
('FileStorage.ServerIPs', '*************,*************', '文件存储服务器IP列表，多个IP用逗号分隔', 'FileStorage', 1, now())
on duplicate key update UpdatedTime = now(); 

-- 增加失败通知列表字段
alter table ExePrograms add column NotificationResourceMachines varchar(255) null comment '失败通知资源机列表，多个用|分隔';