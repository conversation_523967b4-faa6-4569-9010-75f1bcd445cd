import axios from 'axios'

// 获取文件列表
export function listFiles(query) {
  return axios.get('/api/filestorage/list', {
    params: query
  })
}

// 上传文件
export function uploadFile(formData) {
  return axios.post('/api/filestorage/upload', formData)
}

// 下载文件
export function downloadFile(id) {
  return axios.get(`/api/filestorage/${id}`, { 
    responseType: 'blob',
    headers: { 'Accept': 'application/octet-stream' }
  })
}

// 更新文件备注
export function updateFileRemark(id, remark) {
  return axios.put(`/api/filestorage/${id}/remark`, remark, {
    headers: { 'Content-Type': 'application/json' }
  })
}

// 删除单个文件
export function deleteFile(id) {
  return axios.delete(`/api/filestorage/${id}`)
}

// 批量删除文件
export function batchDeleteFiles(ids) {
  return axios.post('/api/filestorage/batchdelete', ids)
} 