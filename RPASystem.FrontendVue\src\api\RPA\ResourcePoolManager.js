import axios from 'axios'

// 获取所有资源池
export function listResourcePools() {
  return axios.get('/api/resourcepool')
}

// 获取所有可用资源机
export function listResourceMachines() {
  return axios.get('/api/resourcepool/machines')
}

// 创建资源池
export function createResourcePool(data) {
  return axios.post('/api/resourcepool', data)
}

// 更新资源池
export function updateResourcePool(data) {
  return axios.put('/api/resourcepool', data)
}

// 删除资源池
export function deleteResourcePool(id) {
  return axios.delete(`/api/resourcepool/${id}`)
} 