﻿using System.ComponentModel.DataAnnotations.Schema;

namespace RPASystem.Model
{
    /// <summary>
    /// 任务模型
    /// </summary>
    public class JobTask
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long ID { get; set; }
        /// <summary>
        /// 程序类型
        /// </summary>
        public long ExeProgramID { get; set; }
        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }
        /// <summary>
        /// 任务优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 任务创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 任务分配的资源机名称
        /// </summary>
        public string? AssignedResourceMachine { get; set; }
        /// <summary>
        /// 程序输入参数（JSON）
        /// </summary>
        public string? InputParameters { get; set; }
        /// <summary>
        /// 程序输出结果
        /// </summary>
        public string? OutputResults { get; set; }
        /// <summary>
        /// 任务状态
        /// </summary>
        [Column(TypeName = "ENUM('Pending', 'Running', 'Success', 'Failed', 'Cancelled','Cancelling')")]
        public JobTaskStatusEnum Status { get; set; }
        /// <summary>
        /// 运行开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 运行结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
        /// <summary>
        /// 任务分配的资源机或资源池
        /// </summary>
        public string? ResourceSelection { get; set; }
        /// <summary>
        /// 父任务ID
        /// </summary>
        public long ParentTaskID { get; set; }
        /// <summary>
        /// 任务类型（普通任务=0、编排任务=1、系统编排拆分任务=2）
        /// </summary>
        [Column(TypeName = "ENUM('Normal', 'Orchestration', 'SystemOrchestrationSplit')")]
        public TaskType TaskType { get; set; } 
        /// <summary>
        /// 程序输出文件
        /// </summary>
        public string? OutputFile { get; set; }
        /// <summary>
        /// 重试次数
        /// </summary>
        public short RetryCount { get; set; }
    }

    public enum TaskType
    {
        Normal = 0, // 普通任务
        Orchestration = 1, // 编排任务
        SystemOrchestrationSplit = 2 // 系统编排拆分任务
    }
}
