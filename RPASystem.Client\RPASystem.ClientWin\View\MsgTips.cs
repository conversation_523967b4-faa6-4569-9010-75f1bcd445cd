﻿using Sunny.UI;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace RPASystem.Client.View
{
    public partial class MsgTips : UIForm
    {
        private static MsgTips instance;
        private string url;
        private static SynchronizationContext uiContext;

        public static void Initialize()
        {
            if (uiContext == null)
            {
                uiContext = SynchronizationContext.Current;
            }
        }

        public MsgTips()
        {
            InitializeComponent();
            ShowInTaskbar = false;
            TopMost = true;

            this.Controls["linkUrl"].Click += new EventHandler(this.linkDetail_Click);
        }

        public static void Show(string message, string url = null)
        {
            if (uiContext == null)
            {
                return;
            }
            if (string.IsNullOrEmpty(url))
            {
                url = MachineMan.BaseUrlFrontend + "/RPA/JobTaskManager?status=Failed";
            }

            uiContext.Post(_ => ShowOnUIThread(message, url), null);
        }

        private static void ShowOnUIThread(string message, string url)
        {
            if (instance == null || instance.IsDisposed)
            {
                instance = new MsgTips();
            }

            instance.UpdateContent(message, url);

            if (!instance.Visible)
            {
                instance.Show();
            }
            instance.BringToFront();
        }

        private void UpdateContent(string message, string url)
        {
            this.Controls["lblMsg"].Text = message;
            this.url = url;
            this.Controls["linkUrl"].Visible = !string.IsNullOrEmpty(url);
        }

        private void MsgTips_Load(object sender, EventArgs e)
        {
            Rectangle workingArea = Screen.PrimaryScreen.WorkingArea;
            this.Location = new Point(workingArea.Right - this.Width, workingArea.Bottom - this.Height);
        }

        private void linkDetail_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(url))
            {
                try
                {
                    Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    UIMessageBox.ShowError("无法打开链接: " + ex.Message);
                }
            }
            this.Close();
        }
    }
}
