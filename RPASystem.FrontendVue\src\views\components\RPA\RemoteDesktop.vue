<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'远程桌面 - ' + machineName"
    fullscreen
    :modal="true"
    :close-on-click-modal="false"
    :before-close="handleClose"
    destroy-on-close
    class="remote-dialog"
  >
    <div 
      class="remote-desktop-container" 
      tabindex="0" 
      ref="desktopContainer"
      @keydown="handleKeyDown" 
      @keyup="handleKeyUp"
      @click="handleContainerClick"
    >
      <div class="image-wrapper">
        <img 
          v-if="screenImage" 
          :src="screenImage" 
          class="desktop-image" 
          @click="handleMouseClick($event, 'left')"
          @contextmenu.prevent="handleMouseClick($event, 'right')"
          @wheel="handleMouseWheel"
        />
        <div v-else class="loading-text">正在连接远程桌面...</div>
      </div>
      <div class="control-buttons">
        <div 
          class="remote-control-button" 
          :class="{ active: isControlActive }" 
          @click="toggleControl"
        >
          远程控制 {{ isControlActive ? '(已启用)' : '(未启用)' }}
        </div>
        <div class="close-button" @click="handleClose">关闭 (ESC)</div>
      </div>
      <div v-if="isControlActive" class="control-info">
        远程控制已启用: 点击鼠标左键或右键可控制远程计算机，滚轮可滚动页面，键盘按键也将传输至远程计算机
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as signalR from '@microsoft/signalr'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  machineName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const screenImage = ref('')
const isControlActive = ref(false)
let connection = null
const desktopContainer = ref(null)
const pressedKeys = ref(new Set()) // 存储当前按下的键

// 开始监视远程桌面
const startWatching = async () => {
  try {
    await connection.invoke('StartWatching', props.machineName)
    // 自动聚焦到桌面容器
    nextTick(() => {
      if (desktopContainer.value) {
        desktopContainer.value.focus()
      }
    })
  } catch (error) {
    console.error('开始远程失败:', error)
    ElMessage.error('连接远程桌面失败')
    dialogVisible.value = false
  }
}

// 停止监视远程桌面
const stopWatching = async () => {
  try {
    if (connection && connection.state === signalR.HubConnectionState.Connected) {
      await connection.invoke('StopWatching', props.machineName)
      screenImage.value = ''
    }
  } catch (error) {
    console.error('停止远程失败:', error)
  }
}

// 处理关闭对话框
const handleClose = () => {
  stopWatching()
  dialogVisible.value = false
}

// 切换远程控制状态
const toggleControl = () => {
  isControlActive.value = !isControlActive.value
  if (isControlActive.value) {
    ElMessage.success('远程控制已启用')
    // 启用控制后自动聚焦
    nextTick(() => {
      if (desktopContainer.value) {
        desktopContainer.value.focus()
      }
    })
  } else {
    ElMessage.info('远程控制已禁用')
  }
}

// 处理鼠标点击事件
const handleMouseClick = async (event, button) => {
  if (!isControlActive.value) return
  
  try {
    const imgElement = event.target
    const rect = imgElement.getBoundingClientRect()
    
    // 计算点击在图像上的相对坐标
    const x = Math.round(((event.clientX - rect.left) / rect.width) * 100)
    const y = Math.round(((event.clientY - rect.top) / rect.height) * 100)
    
    // 调用远程控制方法
    await connection.invoke('SendMouseCommand', props.machineName, button, x, y)
  } catch (error) {
    console.error('发送鼠标命令失败:', error)
    ElMessage.error('远程控制操作失败')
  }
}

// 处理鼠标滚轮事件
const handleMouseWheel = async (event) => {
  if (!isControlActive.value) return
  
  try {
    // 阻止默认滚动行为
    event.preventDefault()
    
    const imgElement = event.target
    const rect = imgElement.getBoundingClientRect()
    
    // 计算点击在图像上的相对坐标
    const x = Math.round(((event.clientX - rect.left) / rect.width) * 100)
    const y = Math.round(((event.clientY - rect.top) / rect.height) * 100)
    
    // 获取滚轮方向和距离
    // deltaY 为正表示向下滚动，为负表示向上滚动
    // 反转方向：向下滚动时发送-1，向上滚动时发送1
    const scrollAmount = event.deltaY > 0 ? -1 : 1
    
    // 调用远程控制方法
    await connection.invoke('SendMouseWheelCommand', props.machineName, scrollAmount, x, y)
  } catch (error) {
    console.error('发送鼠标滚轮命令失败:', error)
    ElMessage.error('远程控制操作失败')
  }
}

// 处理键盘按下事件
const handleKeyDown = async (event) => {
  if (!isControlActive.value) return
  
  try {
    // 阻止默认按键行为，避免触发浏览器快捷键
    event.preventDefault()
    
    // 记录按下的键
    pressedKeys.value.add(event.key)
    
    // 构建当前按下的组合键
    const keyCombination = Array.from(pressedKeys.value)
    
    // 发送键盘按下事件到服务器
    await connection.invoke('SendKeyboardCommand', 
      props.machineName, 
      'keydown',
      event.key,
      keyCombination,
      event.keyCode || 0
    )
  } catch (error) {
    console.error('发送键盘按下命令失败:', error)
    ElMessage.error('远程控制键盘操作失败')
  }
}

// 处理键盘释放事件
const handleKeyUp = async (event) => {
  if (!isControlActive.value) return
  
  try {
    // 阻止默认按键行为
    event.preventDefault()
    
    // 从记录中移除释放的键
    pressedKeys.value.delete(event.key)
    
    // 发送键盘释放事件到服务器
    await connection.invoke('SendKeyboardCommand', 
      props.machineName, 
      'keyup',
      event.key,
      Array.from(pressedKeys.value),
      event.keyCode || 0
    )
  } catch (error) {
    console.error('发送键盘释放命令失败:', error)
    ElMessage.error('远程控制键盘操作失败')
  }
}

// 确保容器获得焦点
const handleContainerClick = () => {
  if (desktopContainer.value) {
    desktopContainer.value.focus()
  }
}

// 将ArrayBuffer转换为Base64
const arrayBufferToBase64 = (buffer) => {
  try {
    let binary = ''
    const bytes = new Uint8Array(buffer)
    const chunkSize = 1024
    for (let i = 0; i < bytes.length; i += chunkSize) {
      const chunk = bytes.slice(i, Math.min(i + chunkSize, bytes.length))
      chunk.forEach(byte => {
        binary += String.fromCharCode(byte)
      })
    }
    return window.btoa(binary)
  } catch (error) {
    console.error('Base64转换失败:', error)
    throw error
  }
}

onMounted(async () => {
  // 初始化SignalR连接
  connection = new signalR.HubConnectionBuilder()
    .withUrl(`/resourceMachineHub`)
    .withHubProtocol(new signalR.JsonHubProtocol())
    .withAutomaticReconnect()
    .configureLogging(signalR.LogLevel.Information)
    .build()

  // 处理接收到的截图数据
  connection.on('ReceiveScreenshot', (imageData) => {
    try {
      if (typeof imageData === 'string') {
        screenImage.value = `data:image/jpeg;base64,${imageData}`
        return
      }
      
      if (imageData instanceof ArrayBuffer) {
        const base64Data = arrayBufferToBase64(imageData)
        screenImage.value = `data:image/jpeg;base64,${base64Data}`
        return
      }
    } catch (error) {
      console.error('处理截图数据失败:', error)
    }
  })

  // 处理机器断开连接
  connection.on('MachineDisconnected', (machineName) => {
    if (props.machineName === machineName) {
      ElMessage.error('远程机器已断开连接')
      dialogVisible.value = false
    }
  })

  // 处理远程控制结果反馈
  connection.on('ReceiveMouseCommandResult', (success, message) => {
    if (!success) {
      ElMessage.error(`远程控制操作失败: ${message}`)
    }
  })
  
  // 处理键盘控制结果反馈
  connection.on('ReceiveKeyboardCommandResult', (success, message) => {
    if (!success) {
      ElMessage.error(`远程控制键盘操作失败: ${message}`)
    }
  })

  try {
    await connection.start()
    if (dialogVisible.value) {
      await startWatching()
    }
  } catch (err) {
    console.error('SignalR Connection Error: ', err)
  }
})

onUnmounted(() => {
  if (connection) {
    stopWatching()
    connection.stop()
  }
})

// 监听对话框可见性变化
watch(() => dialogVisible.value, async (newVal) => {
  if (newVal) {
    await startWatching()
  } else {
    await stopWatching()
    isControlActive.value = false
  }
})
</script>

<style scoped>
.remote-dialog :deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
}

.remote-dialog :deep(.el-dialog__header) {
  padding: 15px;
  margin: 0;
}

.remote-dialog :deep(.el-dialog__body) {
  flex: 1;
  padding: 0;
  margin: 0;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.remote-desktop-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  outline: none; /* 移除焦点时的轮廓 */
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.desktop-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.loading-text {
  color: #fff;
  font-size: 18px;
}

.control-buttons {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1000;
}

.close-button,
.remote-control-button {
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.close-button:hover,
.remote-control-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.remote-control-button.active {
  background-color: rgba(0, 128, 0, 0.7);
}

.control-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 4px;
  z-index: 1000;
}
</style> 