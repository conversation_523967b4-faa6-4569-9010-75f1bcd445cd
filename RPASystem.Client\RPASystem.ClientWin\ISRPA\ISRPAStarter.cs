﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO.Compression;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using OfficeOpenXml;
using System.Collections.Generic;
using RPASystem.Client;
using NLog;
using System.Net.Http.Headers;
using System.Threading;
using RPASystem.Model;

public class ISRPAStarter
{
    private static readonly ILogger logger = LogManager.GetCurrentClassLogger();
    private readonly string rpaName;
    private readonly JobModel jobModel;
    private readonly HttpClient httpClient;
    private string ServerIP;
    string FileId;
    //string TaskOutputServerDir => Path.Combine($@"\\{ServerIP}", "VShare", "WAutoDeploy", jobModel.ProgramName, "OUTPUTRPASYSTEM", jobModel.TopJobTaskName, jobModel.TopJobTaskName, jobModel.JobTaskName);


    string InputFilePath => Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Input), $"RPA_List-{jobModel.JobTaskName}.xlsx");

    string TaskUploadDir => Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Root), jobModel.JobTaskName);
    //string RpaListFileName => $"RPA_List-{DateTime.Now.ToString("yyyy-MM-dd-HHmmss")}.xlsx";

    // string TaskOutputServerDir => $@"\\{ServerIP}\VShare\WAutoDeploy\{jobModel.ProgramName}\OUTPUTRPASYSTEM\{jobModel.TopJobTaskName}\{ParentTaskName}\{jobModel.JobTaskName}";
    string ParentTaskName
    {
        get
        {
            var match = System.Text.RegularExpressions.Regex.Match(jobModel.JobTaskName, @"(.+)_\d+$");
            // 如果ParentTaskName和TopJobTaskName相同，证明是系统编排拆分任务类型，所以不用再加一层目录
            return match.Success ? (match.Groups[1].Value == jobModel.TopJobTaskName ? "" : match.Groups[1].Value) : "";
        }
    }//匹配_数字结尾的字符串，返回_数字之前的部分，如果匹配失败返回空字符串. 

    public ISRPAStarter(JobModel model)
    {
        ServerIP = model.UploadServerIP;
        jobModel = model;
        rpaName = model.ProgramName;
        httpClient = new HttpClient();
    }

    public async Task<RunResult> FastRunAsync(CancellationToken cancellationToken = default)
    {
        var result = new RunResult();

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            await new PackagesMan(jobModel.ProgramName, RPACaseUtils.GetPath(rpaName, RPAFolder.Root), jobModel.ProgramVersion).CheckAndUpdateAsync();
            CloseAllRPA();
            DeleteProjectInt();
            DeleteConfigLogOutput();
            ShowDesktop();

            await ProcessRPAParameters();

            using (var rpaProcess = GetFastRunProcess())
            {
                cancellationToken.Register(() =>
                {
                    try { if (!rpaProcess.HasExited) rpaProcess.Kill(); }
                    catch (Exception ex) { logger.Error(ex, "终止RPA进程失败"); }
                });

                rpaProcess.Start();
                Console.WriteLine($"已启动艺赛旗参数：{rpaProcess.StartInfo.FileName} {rpaProcess.StartInfo.Arguments}");
                rpaProcess.BeginOutputReadLine();

                await Task.Run(() => rpaProcess.WaitForExit(), cancellationToken);

                var rpaResult = RunResult.FromJson(await ISRPALog.GetInstance.GetRpaRetStringAsync(RPACaseUtils.GetPath(rpaName, RPAFolder.Log)));
                result.IsSucceed = rpaResult?.Status > 0;
                result.ReturnResult = rpaResult?.ReturnResult; // string.IsNullOrWhiteSpace(rpaResult?.ReturnResult) ? TaskOutputServerDir : rpaResult.ReturnResult;
                result.ErrorMessage = rpaResult?.ErrorMessage;
                result.Status = rpaResult?.Status;
            }

            cancellationToken.ThrowIfCancellationRequested();

            if (!await ISRPALog.GetInstance.IsRpaCorrectRunCompletedAsync(jobModel.ProgramName))
            {
                ISRPALog.GetInstance.RpaCorrectRunCompletedRecord(jobModel.ProgramName);
                result.ReturnResult = string.IsNullOrEmpty(result.ReturnResult) ? "RPA非正常运行结束" : $"{result.ReturnResult} RPA非正常运行结束";
                result.Status = 0;
                result.IsSucceed = false;
            }

            await ProcessRPAResults();
            result.OutputFileLocalPath = TaskUploadDir;
        }
        catch (Exception ex)
        {
            result.IsSucceed = false;
            result.Status = 0;

            if (ex is OperationCanceledException)
            {
                result.ReturnResult = "任务被取消";
                result.Status = 3;
            }
            else
            {
                result.ReturnResult = $"RPA异常：{ex.Message}";
                logger.Error(ex, $"运行RPA异常：{jobModel.ProgramName} 路径：{RPACaseUtils.GetPath(rpaName, RPAFolder.Root)}");
                Console.WriteLine(result.ErrorMessage);
            }
        }

        return result;
    }

    /// <summary>
    /// 删除文件夹Config Log Output Input
    /// </summary>
    private void DeleteConfigLogOutput()
    {
        // 需要删除的目录路径数组
        string[] pathsToDelete = new[]
        {
            RPACaseUtils.GetPath(rpaName, RPAFolder.Config),
            RPACaseUtils.GetPath(rpaName, RPAFolder.Log),
            RPACaseUtils.GetPath(rpaName, RPAFolder.Output),
            RPACaseUtils.GetPath(rpaName, RPAFolder.Input)
        };

        foreach (string path in pathsToDelete)
        {
            if (Directory.Exists(path))
            {
                RetryH.Execute(() => Directory.Delete(path, true));
            }
        }
    }

    private async Task ProcessRPAParameters()
    {
        var jsonParams = JObject.Parse(jobModel.Parameter);
        var configParams = new Dictionary<string, string>();

        foreach (var param in jsonParams.Properties().ToList())
        {
            if (param.Name == "InputFile")
            {
                var inputVal = param.Value.ToString();
                if (string.IsNullOrEmpty(inputVal))
                {
                    throw new Exception("InputFile 为空！！！");
                }
                // 确保目录存在
                Directory.CreateDirectory(RPACaseUtils.GetPath(rpaName, RPAFolder.Input));
                if (inputVal.StartsWith("\\"))
                    //File.Copy(inputVal, Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Input), RpaListFileName));
                    File.Copy(inputVal, InputFilePath);
                else
                    await DownloadAndSaveInputFile(inputVal);
                jsonParams.Remove("InputFile");
            }
            else if (param.Name == "UserName")
            {
                // 获取用户密文
                var response = await httpClient.GetAsync($"{MachineMan.BaseUrl}/api/RpaCredential/password/{param.Value}");
                if (response.IsSuccessStatusCode)
                {
                    var password = await response.Content.ReadAsStringAsync();
                    configParams.Add("PassWord", password); // 使用固定的 PassWord 作为密文的 KEY
                    configParams.Add(param.Name, param.Value.ToString());
                }
                else
                {
                    throw new Exception($"获取用户密文失败: {response.StatusCode}");
                }
            }
            //else if (param.Name == "ServerIP")
            //{
            //    ServerIP = param.Value.ToString();
            //    configParams.Add(param.Name, ServerIP);
            //    jsonParams.Remove("ServerIP");
            //}
            else
            {
                configParams.Add(param.Name, param.Value.ToString());
            }
        }

        SaveConfigToExcel(configParams);
    }

    private async Task DownloadAndSaveInputFile(string fileId)
    {
        var response = await httpClient.GetAsync($"{MachineMan.BaseUrl}/api/FileStorage/{fileId}");
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsByteArrayAsync();
            //string inputPath = Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Input), RpaListFileName);

            File.WriteAllBytes(InputFilePath, content);
            FileId = fileId;
        }
        else
        {
            throw new Exception($"下载InputFile失败: {response.StatusCode}");
        }
    }

    private void SaveConfigToExcel(Dictionary<string, string> configParams)
    {
        string configPath = Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Config), "RPA_Config.xlsx");

        Directory.CreateDirectory(Path.GetDirectoryName(configPath));

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        using (var package = new ExcelPackage(new FileInfo(configPath)))
        {
            var worksheet = package.Workbook.Worksheets.Add("Config");

            // 添加表头
            worksheet.Cells[1, 1].Value = "KEY";
            worksheet.Cells[1, 2].Value = "VALUE";

            // 从第二行开始写入数据
            int row = 2;
            foreach (var param in configParams)
            {
                worksheet.Cells[row, 1].Value = param.Key;
                worksheet.Cells[row, 2].Value = param.Value;
                row++;
            }
            package.Save();
        }
    }

    private async Task ProcessRPAResults()
    {
        // 将最终状态写入输入件中

        if (!string.IsNullOrEmpty(ServerIP))
        {
            //CopyOutputToServer();
            MoveOutputToNewDir();
        }
        await UploadUpdatedInputFile();

    }

    // 将Output文件夹，移动到当前目录的: 任务号\Output 目录中
    private void MoveOutputToNewDir()
    {
        // 将Output 目录下的所有文件和子目录移动到 TaskUploadDir 目录下的 Output 子目录中
        var outputDir = RPACaseUtils.GetPath(rpaName, RPAFolder.Output);
        var newOutputDir = Path.Combine(TaskUploadDir, "Output");
        FileOrDirOp.Perform(outputDir, newOutputDir, FileOrDirOp.OperationType.Cut);


        // Input 目录下所有文件 复制到 TaskUploadDir 目录下， 1，后面要用到Input 2，可能会有占用问题
        var inputDir = RPACaseUtils.GetPath(rpaName, RPAFolder.Input);
        FileOrDirOp.Perform(inputDir, TaskUploadDir, FileOrDirOp.OperationType.Copy);


        // Config 下所有文件 移动到 TaskUploadDir
        var configDir = RPACaseUtils.GetPath(rpaName, RPAFolder.Config);
        FileOrDirOp.Perform(configDir, TaskUploadDir, FileOrDirOp.OperationType.Copy);

        // Log 目录下所有文件 移动到 TaskUploadDir 目录下
        var logDir = RPACaseUtils.GetPath(rpaName, RPAFolder.Log);
        FileOrDirOp.Perform(logDir, TaskUploadDir, FileOrDirOp.OperationType.Cut);
    }

    //private void CopyOutputToServer()
    //{
    //    Directory.CreateDirectory(TaskOutputServerDir);

    //    var pathsToCopy = new[] { RPACaseUtils.GetPath(rpaName, RPAFolder.Output), RPACaseUtils.GetPath(rpaName, RPAFolder.Input), RPACaseUtils.GetPath(rpaName, RPAFolder.Log) }; // 复制目录

    //    foreach (var sourcePath in pathsToCopy)
    //    {
    //        if (!string.IsNullOrWhiteSpace(sourcePath) && Directory.Exists(sourcePath))
    //        {
    //            string dirName = Path.GetFileName(sourcePath.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar));
    //            string destPath = Path.Combine(TaskOutputServerDir, dirName);
    //            CopyDirectoryRecursively(sourcePath, destPath);
    //        }
    //    }
    //}

    private void CopyDirectoryRecursively(string sourcePath, string targetPath)
    {
        Directory.CreateDirectory(targetPath);
        foreach (string file in Directory.GetFiles(sourcePath))
        {
            File.Copy(file, Path.Combine(targetPath, Path.GetFileName(file)), true);
        }
        foreach (string directory in Directory.GetDirectories(sourcePath))
        {
            string targetDir = Path.Combine(targetPath, Path.GetFileName(directory));
            CopyDirectoryRecursively(directory, targetDir);
        }
    }

    private async Task UploadUpdatedInputFile()
    {
        if (string.IsNullOrEmpty(FileId))
        {
            return;
        }

        if (!File.Exists(InputFilePath))
        {
            throw new FileNotFoundException($"文件未找到: {InputFilePath}");
        }

        using (var httpClient = new HttpClient())
        {
            // 构建目标 URL，包含文件 ID
            string requestUri = $"{MachineMan.BaseUrl}/api/FileStorage/{FileId}";

            using (var content = new MultipartFormDataContent())
            {
                // 读取文件内容
                byte[] fileBytes = File.ReadAllBytes(InputFilePath);
                var fileContent = new ByteArrayContent(fileBytes);
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                // 添加文件到 MultipartFormDataContent，字段名必须与服务端参数匹配
                content.Add(fileContent, "file", jobModel.JobTaskName);

                // 添加备注信息
                string remark = $"{DateTime.Now:yyyyMMddHHmmss} 已更新文件。";
                content.Add(new StringContent(remark), "remark");

                // 发送 PUT 请求
                HttpResponseMessage response = await httpClient.PutAsync(requestUri, content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("文件更新成功。");
                }
                else
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"上传更新后的 InputFile 失败: {response.StatusCode}, 详情: {responseContent}");
                }
            }
        }
    }

    private Process GetFastRunProcess()
    {
        var codesDirPath = Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Root), "Application", "codes");
        var mainPyPath = Path.Combine(codesDirPath, "Main.py");
        var pythonClassName = ISRPAUtils.GetPythonClassName(mainPyPath);
        var pythonExePath = Path.Combine(ISRPAUtils.GetInstallPath(), "Python", "pythons.exe");

        var pythonProcess = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = pythonExePath,
                Arguments = $"\"{mainPyPath}\" -p \"{pythonClassName}\" {GetRpaParam()}",
                WorkingDirectory = codesDirPath,
                CreateNoWindow = true,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                Verb = "runas"
            }
        };

        pythonProcess.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                var message = Encoding.UTF8.GetString(Encoding.Default.GetBytes(e.Data));
                ISRPALog.GetInstance.WriteToLogFile(jobModel.ProgramName, message);
            }
        };

        return pythonProcess;
    }

    private string GetRpaParam()
    {
        return "";
        //return !string.IsNullOrEmpty(jobModel.Parameter) ? $"-i\"{Convert.ToBase64String(Encoding.UTF8.GetBytes(jobModel.Parameter))}\"" : string.Empty;
    }

    private void DeleteProjectInt()
    {
        string pathProject = Path.Combine(RPACaseUtils.GetPath(rpaName, RPAFolder.Root), "Application", "Project.int");
        if (File.Exists(pathProject))
        {
            File.Delete(pathProject);
        }
    }

    public static void CloseAllRPA()
    {
        var processNames = new[] { "RPAUpdate", "RPAStudio", "pythons" };
        foreach (var processName in processNames)
        {
            foreach (var process in Process.GetProcessesByName(processName))
            {
                try
                {
                    process.Kill();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"无法结束进程 {processName}：{ex.Message}");
                }
            }
        }
    }

    private static void ShowDesktop()
    {
        try
        {
            Keybd_event((byte)Keys.LWin, 0, 0, 0);
            Keybd_event((byte)Keys.M, 0, 0, 0);
            Keybd_event((byte)Keys.M, 0, 2, 0);
            Keybd_event((byte)Keys.LWin, 0, 2, 0);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"显示桌面失败：{ex.Message}");
        }
    }

    [DllImport("user32.dll", EntryPoint = "keybd_event", SetLastError = true)]
    private static extern void Keybd_event(byte key, byte bscan, uint dwflags, uint dwextraInfo);
}
