import axios from 'axios'

// 获取任务列表
export function searchJobTasks(params) {
  return axios.get('/api/jobtask/search', { params })
}

// 获取所有程序列表
export function getAllExePrograms() {
  return axios.get('/api/exeprogram/GetAllExeProgramsForList')
}

// 获取程序详情
export function getExeProgramDetail(programId) {
  return axios.get(`/api/exeprogram/${programId}`)
}

// 创建编排任务
export function createOrchestrationTask(taskData) {
  // 对于拆分任务，设置更长的超时时间（30分钟）
  const timeout = taskData.taskType === 2 ? 30 * 60 * 1000 : 30000
  return axios.post('/api/orchestration/createOrcJobTask', taskData, { timeout })
}

// 删除任务
export function deleteJobTask(jobTaskId) {
  return axios.delete(`/api/jobtask/${jobTaskId}`)
}

// 重试任务
export function retryJobTask(jobTaskId) {
  return axios.post(`/api/jobtask/retry/${jobTaskId}`)
}

// 停止任务
export function stopJobTask(jobTaskId) {
  return axios.post(`/api/jobtask/stop/${jobTaskId}`)
}

// 更新任务
export function updateJobTask(jobTaskId, updateData) {
  return axios.put(`/api/JobTask/update/${jobTaskId}`, updateData)
}

// 下载文件
export function downloadFile(fileId) {
  return axios.get(`/api/filestorage/${fileId}`, { 
    responseType: 'blob',
    headers: { 'Accept': 'application/octet-stream' }
  })
}

// 获取资源池列表
export function getResourcePools() {
  return axios.get('/api/ResourcePool')
}

// 获取资源机列表
export function getResourceMachines() {
  return axios.get('/api/ResourcePool/machines')
}

// 获取RPA账号凭证列表
export function getRpaCredentials() {
  return axios.get('/api/RpaCredential')
} 