using RPASystem.Model;

public class ViewJobTaskExeProgram
{
    public long JobTaskId { get; set; }
    public string ExeProgramName { get; set; }
    public string JobTaskName { get; set; }
    public int Priority { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string AssignedResourceMachine { get; set; }
    public string InputParameters { get; set; }
    public string OutputResults { get; set; }
    public string Status { get; set; }
    public string Notes { get; set; }
    public long ParentTaskID { get; set; }
    public TaskType TaskType { get; set; }
    public string ResourceSelection { get; set; }
    public ChildTaskStats ChildTaskStats { get; set; }
    public string Version { get; set; }
    public ProgramTypeEnum ProgramType { get; set; }
    public long ExeProgramId { get; set; }
    /// <summary>
    /// 是否独占资源机
    /// </summary>
    public bool IsExclusive { get; set; }
    /// <summary>
    /// 程序输出文件
    /// </summary>
    public string OutputFile { get; set; }
    /// <summary>
    /// 重试次数
    /// </summary>
    public short RetryCount { get; set; }
}

public class ChildTaskStats
{
    public int Total { get; set; }
    public int Success { get; set; }
    public int Running { get; set; }
    public int Failed { get; set; }
    public int Pending { get; set; }
    public int Cancelled { get; set; }
} 