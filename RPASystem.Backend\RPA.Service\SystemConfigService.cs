using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using Infrastructure.Attribute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(ISystemConfigService), ServiceLifetime = LifeTime.Scoped)]
    public class SystemConfigService : ISystemConfigService
    {
        private readonly RPASystemDbContext db;

        public SystemConfigService(RPASystemDbContext context)
        {
            this.db = context;
        }

        public async Task<List<SystemConfig>> GetAllConfigsAsync()
        {
            return await db.SystemConfigs.OrderBy(c => c.ConfigGroup).ThenBy(c => c.ConfigKey).ToListAsync();
        }

        public async Task<List<SystemConfig>> GetConfigsByGroupAsync(string group)
        {
            return await db.SystemConfigs
                .Where(c => c.ConfigGroup == group)
                .OrderBy(c => c.Config<PERSON>ey)
                .ToListAsync();
        }

        public async Task<SystemConfig> GetConfigByIdAsync(long id)
        {
            return await db.SystemConfigs.FindAsync(id);
        }

        public async Task<SystemConfig> GetConfigByKeyAsync(string key)
        {
            return await db.SystemConfigs.FirstOrDefaultAsync(c => c.ConfigKey == key);
        }

        public async Task<string> GetConfigValueAsync(string key, string defaultValue = "")
        {
            var config = await db.SystemConfigs.FirstOrDefaultAsync(c => c.ConfigKey == key);
            return config?.ConfigValue ?? defaultValue;
        }

        public async Task<bool> CreateConfigAsync(SystemConfig config)
        {
            try
            {
                // 检查键名是否已存在
                var exists = await db.SystemConfigs.AnyAsync(c => c.ConfigKey == config.ConfigKey);
                if (exists)
                {
                    throw new Exception($"配置键 '{config.ConfigKey}' 已存在");
                }

                config.CreatedTime = DateTime.Now;
                await db.SystemConfigs.AddAsync(config);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> UpdateConfigAsync(SystemConfig config)
        {
            try
            {
                var existing = await db.SystemConfigs.FindAsync(config.ID);
                if (existing == null) return false;

                // 如果键名发生变化，检查新键名是否已存在
                if (existing.ConfigKey != config.ConfigKey)
                {
                    var exists = await db.SystemConfigs.AnyAsync(c => c.ConfigKey == config.ConfigKey && c.ID != config.ID);
                    if (exists)
                    {
                        throw new Exception($"配置键 '{config.ConfigKey}' 已存在");
                    }
                }

                // 如果是系统内置配置，不允许修改键名和分组
                if (existing.IsSystem)
                {
                    config.ConfigKey = existing.ConfigKey;
                    config.ConfigGroup = existing.ConfigGroup;
                    config.IsSystem = true;
                }

                existing.ConfigKey = config.ConfigKey;
                existing.ConfigValue = config.ConfigValue;
                existing.Description = config.Description;
                existing.ConfigGroup = config.ConfigGroup;
                existing.UpdatedTime = DateTime.Now;

                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> DeleteConfigAsync(long id)
        {
            try
            {
                var config = await db.SystemConfigs.FindAsync(id);
                if (config == null) return false;

                // 系统内置配置不允许删除
                if (config.IsSystem)
                {
                    throw new Exception("系统内置配置不允许删除");
                }

                db.SystemConfigs.Remove(config);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> BatchUpdateConfigsAsync(List<SystemConfigDto> configs)
        {
            try
            {
                foreach (var configDto in configs)
                {
                    if (configDto.ID.HasValue)
                    {
                        // 更新现有配置
                        var existing = await db.SystemConfigs.FindAsync(configDto.ID.Value);
                        if (existing != null)
                        {
                            // 系统内置配置不允许修改键名和分组
                            if (existing.IsSystem)
                            {
                                existing.ConfigValue = configDto.ConfigValue;
                                existing.Description = configDto.Description;
                            }
                            else
                            {
                                existing.ConfigKey = configDto.ConfigKey;
                                existing.ConfigValue = configDto.ConfigValue;
                                existing.Description = configDto.Description;
                                existing.ConfigGroup = configDto.ConfigGroup;
                            }
                            existing.UpdatedTime = DateTime.Now;
                        }
                    }
                    else
                    {
                        // 检查配置键是否已存在
                        var existingByKey = await db.SystemConfigs.FirstOrDefaultAsync(c => c.ConfigKey == configDto.ConfigKey);
                        
                        if (existingByKey != null)
                        {
                            // 更新已存在的配置
                            // 系统内置配置不允许修改键名和分组
                            if (existingByKey.IsSystem)
                            {
                                existingByKey.ConfigValue = configDto.ConfigValue;
                                existingByKey.Description = configDto.Description;
                            }
                            else
                            {
                                existingByKey.ConfigValue = configDto.ConfigValue;
                                existingByKey.Description = configDto.Description;
                                existingByKey.ConfigGroup = configDto.ConfigGroup;
                            }
                            existingByKey.UpdatedTime = DateTime.Now;
                        }
                        else
                        {
                            // 创建新配置
                            var newConfig = new SystemConfig
                            {
                                ConfigKey = configDto.ConfigKey,
                                ConfigValue = configDto.ConfigValue,
                                Description = configDto.Description,
                                ConfigGroup = configDto.ConfigGroup,
                                IsSystem = configDto.IsSystem,
                                CreatedTime = DateTime.Now
                            };
                            await db.SystemConfigs.AddAsync(newConfig);
                        }
                    }
                }

                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<int> InitializeDefaultConfigsAsync()
        {
            try
            {
                // 检查是否已经初始化
                if (await db.SystemConfigs.AnyAsync(c => c.IsSystem))
                {
                    return 0; // 已经初始化过，不需要再次初始化
                }

                // 默认配置列表
                var defaultConfigs = new List<SystemConfig>
                {
                    new SystemConfig
                    {
                        ConfigKey = "ResourceMachine.MaxConcurrentTasks",
                        ConfigValue = "3",
                        Description = "资源机最大同时运行任务数量",
                        ConfigGroup = "ResourceMachine",
                        IsSystem = true,
                        CreatedTime = DateTime.Now
                    },
                    new SystemConfig
                    {
                        ConfigKey = "ResourceMachine.MemoryThreshold",
                        ConfigValue = "90",
                        Description = "内存使用率阈值（百分比），超过此值将触发重启",
                        ConfigGroup = "ResourceMachine",
                        IsSystem = true,
                        CreatedTime = DateTime.Now
                    },
                    new SystemConfig
                    {
                        ConfigKey = "FileStorage.ServerIPs",
                        ConfigValue = "*************,*************",
                        Description = "文件存储服务器IP列表，多个IP用逗号分隔",
                        ConfigGroup = "FileStorage",
                        IsSystem = true,
                        CreatedTime = DateTime.Now
                    }
                };

                await db.SystemConfigs.AddRangeAsync(defaultConfigs);
                await db.SaveChangesAsync();
                return defaultConfigs.Count;
            }
            catch
            {
                throw;
            }
        }
    }
} 