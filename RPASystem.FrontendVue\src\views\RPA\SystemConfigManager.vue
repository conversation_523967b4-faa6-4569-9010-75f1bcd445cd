<template>
  <div class="system-config-manager">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <div class="left-section">
        <el-input
          v-model="searchQuery"
          placeholder="请输入配置键名或描述搜索"
          style="width: 300px;"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
        <el-select v-model="groupFilter" placeholder="按分组筛选" style="width: 150px; margin-left: 10px;" clearable @change="handleGroupFilterChange">
          <el-option
            v-for="group in configGroups"
            :key="group"
            :label="group"
            :value="group"
          />
        </el-select>
      </div>
      <div class="right-section">
        <el-button type="primary" @click="showAddDialog">新增配置</el-button>
        <el-button type="success" @click="handleInitialize">初始化默认配置</el-button>
      </div>
    </div>

    <!-- 配置列表 -->
    <el-table :data="filteredConfigs" style="width: 100%">
      <el-table-column prop="configKey" label="配置键名" width="220" />
      <el-table-column prop="configValue" label="配置值" width="220" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="configGroup" label="分组" width="120" />
      <el-table-column prop="isSystem" label="系统内置" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isSystem ? 'success' : 'info'">
            {{ scope.row.isSystem ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" width="180" :formatter="formatDateTime" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(scope.row)"
            :disabled="scope.row.isSystem"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑配置' : '新增配置'"
      width="500px"
      destroy-on-close
    >
      <el-form :model="currentConfig" label-width="100px" :rules="rules" ref="formRef">
        <el-form-item label="配置键名" prop="configKey" :disabled="isEdit && currentConfig.isSystem">
          <el-input 
            v-model="currentConfig.configKey" 
            placeholder="请输入配置键名" 
            :disabled="isEdit && currentConfig.isSystem"
          />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input 
            v-model="currentConfig.configValue" 
            placeholder="请输入配置值" 
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="currentConfig.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入描述信息"
          />
        </el-form-item>
        <el-form-item label="分组" prop="configGroup" :disabled="isEdit && currentConfig.isSystem">
          <el-select 
            v-model="currentConfig.configGroup" 
            placeholder="请选择分组"
            style="width: 100%"
            :disabled="isEdit && currentConfig.isSystem"
            filterable
            allow-create
          >
            <el-option
              v-for="group in configGroups"
              :key="group"
              :label="group"
              :value="group"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="系统内置" v-if="isEdit">
          <el-switch v-model="currentConfig.isSystem" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="systemconfigmanager">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  listAllConfigs,
  getConfigsByGroup,
  createConfig,
  updateConfig,
  deleteConfig,
  initializeConfigs
} from '@/api/RPA/SystemConfigManager'

// 数据定义
const configs = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)
const searchQuery = ref('')
const groupFilter = ref('')

const currentConfig = ref({
  configKey: '',
  configValue: '',
  description: '',
  configGroup: '',
  isSystem: false
})

// 获取所有配置分组
const configGroups = computed(() => {
  const groups = new Set()
  configs.value.forEach(config => {
    if (config.configGroup) {
      groups.add(config.configGroup)
    }
  })
  return Array.from(groups)
})

// 根据搜索条件和分组过滤配置列表
const filteredConfigs = computed(() => {
  let result = configs.value

  // 应用分组过滤
  if (groupFilter.value) {
    result = result.filter(config => config.configGroup === groupFilter.value)
  }

  // 应用搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(config => 
      config.configKey.toLowerCase().includes(query) || 
      config.description?.toLowerCase().includes(query) ||
      config.configValue.toLowerCase().includes(query)
    )
  }

  return result
})

// 表单验证规则
const rules = {
  configKey: [
    { required: true, message: '请输入配置键名', trigger: 'blur' }
  ],
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  configGroup: [
    { required: true, message: '请选择或输入分组', trigger: 'change' }
  ]
}

// 获取所有配置
const fetchConfigs = async () => {
  try {
    const response = await listAllConfigs()
    configs.value = response.data
  } catch (error) {
    ElMessage.error('获取配置列表失败')
    console.error('获取配置列表失败:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  // 本地搜索，无需调用后端
}

// 处理分组过滤变更
const handleGroupFilterChange = () => {
  // 本地过滤，无需调用后端
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  currentConfig.value = {
    configKey: '',
    configValue: '',
    description: '',
    configGroup: '',
    isSystem: false
  }
  dialogVisible.value = true
}

// 显示编辑对话框
const handleEdit = (row) => {
  isEdit.value = true
  currentConfig.value = { ...row }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (row) => {
  if (row.isSystem) {
    ElMessage.warning('系统内置配置不能删除')
    return
  }

  try {
    await ElMessageBox.confirm('确定要删除该配置吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    await deleteConfig(row.id)
    ElMessage.success('删除成功')
    await fetchConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  }
}

// 初始化默认配置
const handleInitialize = async () => {
  try {
    await ElMessageBox.confirm('确定要初始化默认配置吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    const response = await initializeConfigs()
    ElMessage.success(`初始化成功，添加了 ${response.data} 条默认配置`)
    await fetchConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('初始化失败')
      console.error('初始化失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await updateConfig(currentConfig.value)
    } else {
      await createConfig(currentConfig.value)
    }
    
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    await fetchConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
    }
  }
}

// 格式化日期时间
const formatDateTime = (row, column, cellValue) => {
  if (!cellValue) return ''
  return new Date(cellValue).toLocaleString()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.system-config-manager {
  padding: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.left-section {
  display: flex;
  align-items: center;
}

.right-section {
  display: flex;
  gap: 10px;
}
</style> 