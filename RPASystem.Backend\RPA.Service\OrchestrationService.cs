using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using RPASystem.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using RPASystem.Service;
using Infrastructure.Attribute;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IOrchestrationService), ServiceLifetime = LifeTime.Scoped)]
    public class OrchestrationService : IOrchestrationService
    {
        private readonly IServiceScopeFactory scopeFactory;

        public OrchestrationService(IServiceScopeFactory scopeFactory)
        {
            this.scopeFactory = scopeFactory;
        }

        /// <summary>
        /// 获取程序类型对应的任务名前缀
        /// </summary>
        /// <param name="programType">程序类型</param>
        /// <param name="taskType">任务类型（可选）</param>
        /// <returns>任务名称前缀</returns>
        private string GetTypePrefixFromProgramType(ProgramTypeEnum programType, TaskType? taskType = null)
        {
            //// 首先根据任务类型判断
            //if (taskType.HasValue)
            //{
            //    switch (taskType.Value)
            //    {
            //        case TaskType.Orchestration:
            //            return "ORC_";
            //        case TaskType.SystemOrchestrationSplit:
            //            return "SYS_";
            //        // 对于普通任务，根据程序类型判断
            //    }
            //}

            // 如果没有特殊任务类型或是普通任务，则根据程序类型判断
            switch (programType)
            {
                case ProgramTypeEnum.RPA:
                    return "RPA_";
                case ProgramTypeEnum.EXE:
                    return "EXE_";
                case ProgramTypeEnum.Orchestration:
                    return "ORC_";
                default:
                    return "RPA_"; // 默认前缀
            }
        }

        /// <summary>
        /// 创建所有类型任务
        /// </summary>
        public async Task<long> CreateOrchestrationTask(OrchestrationTaskDto dto)
        {
            using var scope = scopeFactory.CreateScope();
            var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
            var exeProgramService = scope.ServiceProvider.GetRequiredService<IExeProgramService>();
            var taskNameGenerator = scope.ServiceProvider.GetRequiredService<ITaskNameGenerator>();

            var exeProgram = exeProgramService.GetExeProgramByName(dto.ProgramName);
            if (exeProgram == null)
            {
                throw new Exception($"Program not found: {dto.ProgramName}");
            }

            // 如果为空则生成
            if (string.IsNullOrWhiteSpace(dto.TaskName))
            {
                if (dto.ParentTaskID.HasValue && dto.ParentTaskID.Value > 0)
                {
                    var parentTask = await jobTaskService.GetJobTaskByIdAsync(dto.ParentTaskID.Value);
                    dto.TaskName = await taskNameGenerator.GenerateSubTaskNameAsync(parentTask.TaskName);
                }
                else
                {
                    // 根据程序类型和任务类型获取对应的前缀
                    TaskType taskType = TaskType.Normal;
                    
                    // 判断任务类型
                    if (exeProgram.ProgramType == ProgramTypeEnum.Orchestration)
                    {
                        taskType = TaskType.Orchestration;
                    }
                    else if (exeProgram.ProgramType == ProgramTypeEnum.RPA && 
                            dto.InputParameters != null && 
                            dto.InputParameters.Contains("ExcelPerSplitNum"))
                    {
                        taskType = TaskType.SystemOrchestrationSplit;
                    }
                    
                    string typePrefix = GetTypePrefixFromProgramType(exeProgram.ProgramType, taskType);
                    dto.TaskName = await taskNameGenerator.GenerateTopTaskNameAsync(typePrefix);
                }
            }
            else
            {
                // 如果任务名称不为空，检查是否已存在同名任务
                var existingTask = await jobTaskService.GetJobTaskByNameAsync(dto.TaskName);
                if (existingTask != null)
                {
                    // 任务已存在，直接返回现有任务ID
                    return existingTask.ID;
                }
            }
            var jobTask = new JobTask
            {
                ParentTaskID = dto.ParentTaskID ?? 0,
                ExeProgramID = exeProgram.ID,
                TaskName = dto.TaskName,
                Priority = dto.TaskPriority ?? 0,
                InputParameters = dto.InputParameters,
                Status = JobTaskStatusEnum.Pending,
                CreatedAt = DateTime.Now,
                ResourceSelection = dto.ResourceSelection ?? exeProgram.ResourceSelection,
                Notes = dto.Notes,
            };
            if (exeProgram.ProgramType == ProgramTypeEnum.Orchestration)
            {
                jobTask.Status = JobTaskStatusEnum.Running;
                jobTask.TaskType = TaskType.Orchestration;
                jobTask.StartTime = DateTime.Now;
            }
            else if (exeProgram.ProgramType == ProgramTypeEnum.RPA && dto.InputParameters.Contains("ExcelPerSplitNum"))
            {
                jobTask.TaskType = TaskType.SystemOrchestrationSplit;
                jobTask.StartTime = DateTime.Now;
            }
            else
            {
                jobTask.TaskType = TaskType.Normal;
            }
            var taskId = await jobTaskService.CreateJobTaskAsync(jobTask);

            // 将OutputFile作为参数传递给子任务
            jobTask.OutputFile = dto.OutputFile;
            await ProcessSplitTaskAsync(jobTask);

            return taskId;
        }

        /// <summary>
        /// 处理Excel拆分任务
        /// </summary>
        public async Task ProcessSplitTaskAsync(JobTask jobTask)
        {
            if (jobTask.TaskType == TaskType.SystemOrchestrationSplit)
            {
                using var scope = scopeFactory.CreateScope();
                var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
                var fileStorageService = scope.ServiceProvider.GetRequiredService<IFileStorageService>();

                try
                {
                    // 解析输入参数
                    var inputParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(jobTask.InputParameters);

                    if (!inputParams.TryGetValue("InputFile", out var fileIdStr) || !inputParams.TryGetValue("ExcelPerSplitNum", out var splitNumStr)
                        || !int.TryParse(splitNumStr, out var splitNum))
                    {
                        throw new Exception("缺少必要的输入参数或参数格式错误");
                    }

                    byte[] fileData = null;
                    FileStorage fileStorage = null;
                    string fileExtension = ".xlsx";
                    if (fileIdStr.StartsWith("\\"))
                    {
                        fileData = File.ReadAllBytes(fileIdStr);
                        fileExtension = Path.GetExtension(fileIdStr);
                    }
                    else
                    {
                        if (!long.TryParse(fileIdStr, out var fileId))
                        {
                            throw new Exception($"fileId = {fileIdStr} 格式错误!!!");
                        }
                        // 获取Excel文件数据
                        fileStorage = await fileStorageService.GetFileAsync(fileId);
                        if (fileStorage == null)
                        {
                            throw new Exception($"找不到指定的Excel文件: {fileId}");
                        }
                        fileData = fileStorage.FileData;
                        fileExtension = fileStorage.FileExtension;
                    }

                    // 使用内存流处理Excel文件
                    using (var inputStream = new MemoryStream(fileData))
                    {
                        ExcelSplitter.Instance.SplitExcelByNum(inputStream, splitNum, async (index, splitStream) =>
                        {
                            using var splitScope = scopeFactory.CreateScope();
                            var splitFileService = splitScope.ServiceProvider.GetRequiredService<IFileStorageService>();
                            var splitJobTaskService = splitScope.ServiceProvider.GetRequiredService<IJobTaskService>();

                            // 保存拆分后的Excel文件
                            var newFileStorage = new FileStorage
                            {
                                FileName = $"{jobTask.TaskName}_{index}",
                                FileExtension = fileExtension,
                                FileData = ((MemoryStream)splitStream).ToArray(),
                                UploadTime = DateTime.Now
                            };

                            var newFileId = await splitFileService.UploadFileAsync(newFileStorage);

                            // 创建新的输入参数，替换文件ID
                            var newInputParams = new Dictionary<string, string>(inputParams);
                            newInputParams["InputFile"] = newFileId.ToString();

                            var subTaskName = $"{jobTask.TaskName}__{index}";
                            var subTask = new JobTask
                            {
                                ParentTaskID = jobTask.ID,
                                ExeProgramID = jobTask.ExeProgramID,
                                TaskName = subTaskName,
                                Priority = jobTask.Priority,
                                InputParameters = System.Text.Json.JsonSerializer.Serialize(newInputParams),
                                Status = JobTaskStatusEnum.Pending,
                                CreatedAt = DateTime.Now,
                                TaskType = TaskType.Normal,
                                ResourceSelection = jobTask.ResourceSelection,
                                OutputFile = jobTask.OutputFile
                            };

                            await splitJobTaskService.CreateJobTaskAsync(subTask);
                        });

                        // 更新父任务状态
                        // await jobTaskService.UpdateJobTaskStatusAsync(jobTask.ID, JobTaskStatusEnum.Running);
                    }
                }
                catch (Exception ex)
                {
                    await jobTaskService.UpdateJobTaskStatusAsync(jobTask.ID, JobTaskStatusEnum.Failed, outputResults: "处理Excel拆分任务异常：" + ex.ToString());
                    throw;
                }
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        public async Task<OrchestrationTaskStatusDto> GetOrchestrationTaskStatus(long taskId)
        {
            using var scope = scopeFactory.CreateScope();
            var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();

            var task = await jobTaskService.GetJobTaskByIdAsync(taskId);
            if (task == null)
            {
                throw new Exception($"Task not found: {taskId}");
            }

            return new OrchestrationTaskStatusDto
            {
                TaskId = task.ID,
                Status = task.Status,
                OutputResults = task.OutputResults
            };
        }

        /// <summary>
        /// 通过任务名称获取任务状态
        /// </summary>
        public async Task<OrchestrationTaskStatusDto> GetOrchestrationTaskStatusByName(string taskName)
        {
            using var scope = scopeFactory.CreateScope();
            var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();

            var task = await jobTaskService.GetJobTaskByNameAsync(taskName);
            if (task == null)
            {
                throw new Exception($"Task not found: {taskName}");
            }

            return new OrchestrationTaskStatusDto
            {
                TaskId = task.ID,
                Status = task.Status,
                OutputResults = task.OutputResults
            };
        }

        /// <summary>
        /// 批量获取任务状态
        /// </summary>
        /// <param name="taskNames">任务名称数组</param>
        /// <returns>字典，键为任务名称，值为任务状态</returns>
        public async Task<Dictionary<string, OrchestrationTaskStatusDto>> GetOrchestrationTaskStatusByNames(string[] taskNames)
        {
            if (taskNames == null || taskNames.Length == 0)
            {
                return new Dictionary<string, OrchestrationTaskStatusDto>();
            }

            using var scope = scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<RPASystemDbContext>();

            // 使用IN语句一次性查询所有任务，提高性能
            var tasks = await dbContext.JobTasks
                .Where(t => taskNames.Contains(t.TaskName))
                .ToListAsync();

            // 创建结果字典
            var result = new Dictionary<string, OrchestrationTaskStatusDto>();
            
            // 填充结果字典
            foreach (var task in tasks)
            {
                result[task.TaskName] = new OrchestrationTaskStatusDto
                {
                    TaskId = task.ID,
                    Status = task.Status,
                    OutputResults = task.OutputResults
                };
            }

            return result;
        }
    }
}

