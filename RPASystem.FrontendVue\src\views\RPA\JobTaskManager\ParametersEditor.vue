<template>
  <div>
    <el-form :model="parameters" label-width="120px" ref="formRef" :rules="rules">
      <div v-for="(param, index) in parsedParameters" :key="index" class="parameter-row">
        <span class="parameter-name">
          {{ param.ParametersName }}
          <span v-if="param.IsRequired" class="required-mark">*</span>
        </span>
        
        <el-form-item 
          v-if="param.ParametersType === 'string'"
          :prop="param.ParametersName"
        >
          <el-input 
            v-model="parameters[param.ParametersName]" 
            class="parameter-value" 
            :placeholder="param.IsRequired ? '请输入' + param.ParametersName : ''"
          />
        </el-form-item>
        
        <el-form-item 
          v-if="param.ParametersType === 'int'"
          :prop="param.ParametersName"
        >
          <el-input-number 
            v-model="parameters[param.ParametersName]" 
            class="parameter-value"
          />
        </el-form-item>
        
        <el-input v-if="param.ParametersType === 'bool'" v-model="parameters[param.ParametersName]" class="parameter-value" />
        
        <el-upload
          v-if="param.ParametersType === 'file'"
          :action="uploadUrl"
          :on-success="(response) => handleFileUpload(response, param.ParametersName)"
          :on-error="handleUploadError"
          :before-upload="(file) => beforeFileUpload(file, param.ParametersName)"
          :limit="1"
          :on-exceed="handleExceed"
          class="parameter-value upload-compact"
        >
          <el-button size="small" type="primary">上传文件</el-button>
          <template #tip>
            <div v-if="parameters[param.ParametersName]" class="el-upload__tip uploaded-file">
              已上传: {{ getFileName(parameters[param.ParametersName]) }}
            </div>
            <el-tooltip
              v-if="param.ParametersName === 'InputFile'"
              content="只能上传 xlsx/xls 格式文件"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
        </el-upload>
        
        <el-select 
          v-if="param.ParametersType === 'select'" 
          v-model="parameters[param.ParametersName]"
          :multiple="param.ParametersOptions === '2'"
          class="parameter-value"
        >
          <el-option 
            v-for="option in param.ParametersSelectValue.split('|')" 
            :key="option" 
            :label="option" 
            :value="option"
          />
        </el-select>

        <el-select
          v-if="param.ParametersType === 'ResourceSelection'"
          v-model="parameters[param.ParametersName]"
          multiple
          filterable
          placeholder="请选择资源"
          class="parameter-value"
        >
          <el-option-group label="资源池">
            <el-option
              v-for="pool in resourcePools"
              :key="'pool_' + pool.id"
              :label="pool.poolName"
              :value="pool.poolName"
            />
          </el-option-group>
          <el-option-group label="资源机">
            <el-option
              v-for="machine in resourceMachines"
              :key="'machine_' + machine.id"
              :label="machine.machineName"
              :value="machine.machineName"
            />
          </el-option-group>
        </el-select>

        <el-select
          v-if="param.ParametersType === 'RpaCredentials'"
          v-model="parameters[param.ParametersName]"
          filterable
          placeholder="请选择RPA账号"
          class="parameter-value"
        >
          <el-option
            v-for="credential in rpaCredentials"
            :key="credential.id"
            :label="credential.username"
            :value="credential.username"
          />
        </el-select>

        <span class="parameter-description">{{ param.ParametersDescription }}</span>
      </div>
    </el-form>
  </div>
</template>

<script setup name="JobTaskParametersEditor">
import { ref, watch, computed, onMounted } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  getResourcePools, 
  getResourceMachines, 
  getRpaCredentials 
} from '@/api/RPA/JobTaskManager'

const props = defineProps({
  initialParameters: {
    type: String,
    default: '[]'
  },
  programInputParameters: {
    type: String,
    required: true
  },
  taskType: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:parameters', 'file-type-error'])

const parameters = ref({})
const parsedParameters = ref([])
const resourcePools = ref([])
const resourceMachines = ref([])
const rpaCredentials = ref([])
const formRef = ref(null)
const rules = ref({})

// 处理参数并发送更新
const processParameters = () => {
  const processedParams = {}
  
  // 遍历所有参数
  parsedParameters.value.forEach(param => {
    const value = parameters.value[param.ParametersName]
    
    // 特殊处理资源选择类型
    if (param.ParametersType === 'ResourceSelection' && Array.isArray(value)) {
      // 将数组转换为用"|"分隔的字符串
      processedParams[param.ParametersName] = value.join('|')
    } else {
      processedParams[param.ParametersName] = value
    }
  })

  return processedParams
}

// 监听参数变化
watch(parameters, () => {
  const processedParams = processParameters()
  emit('update:parameters', JSON.stringify(processedParams))
}, { deep: true })

// 初始化表单验证规则
const initializeRules = () => {
  const newRules = {}
  parsedParameters.value.forEach(param => {
    if (param.IsRequired && (param.ParametersType === 'string' || param.ParametersType === 'int')) {
      newRules[param.ParametersName] = [{
        required: true,
        message: `请输入${param.ParametersName}`,
        trigger: 'blur'
      }]
    }
  })
  rules.value = newRules
}

// 初始化参数
const initializeParameters = () => {
  try {
    parsedParameters.value = JSON.parse(props.programInputParameters)
    const initialParams = props.initialParameters ? JSON.parse(props.initialParameters) : {}
    
    // 处理每个参数
    parsedParameters.value.forEach(param => {
      // 如果有初始值，使用初始值
      if (initialParams[param.ParametersName]) {
        parameters.value[param.ParametersName] = initialParams[param.ParametersName]
      } 
      // 否则，如果有默认值且是文本或数字类型，使用默认值
      else if (param.DefaultValue && (param.ParametersType === 'string' || param.ParametersType === 'int')) {
        parameters.value[param.ParametersName] = param.DefaultValue
      } 
      // 其他情况使用空值
      else {
        parameters.value[param.ParametersName] = ''
      }
    })

    // 初始化验证规则
    initializeRules()
  } catch (error) {
    console.error('解析参数失败:', error)
  }
}

// 监听参数变化
watch(() => props.programInputParameters, initializeParameters, { immediate: true })

// 文件上传前的验证
const beforeFileUpload = (file, paramName) => {
  if (paramName === 'InputFile') {
    const fileName = file.name.toLowerCase()
    const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls')
    if (!isExcel) {
      emit('file-type-error', 'InputFile只能上传xlsx或xls格式的文件')
      return false
    }
  }
  return true
}

// 处理文件上传成功
const handleFileUpload = (response, paramName) => {
  if (response.fileId) {
    // 存储文件ID到参数中
    parameters.value[paramName] = response.fileId.toString()
    // 触发参数更新
    emit('update:parameters', JSON.stringify(processParameters()))
  } else {
    console.error('文件上传失败:', response)
  }
}

// 处理上传错误
const handleUploadError = (error) => {
  console.error('文件上传失败:', error)
}

// 获取文件名
const getFileName = (fileId) => {
  // 这里可以添加逻辑来获取文件名，如果需要的话
  return fileId
}

// 上传URL
const uploadUrl = computed(() => `/api/FileStorage/uploadForTask`)

// 处理超出上传限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件，请先删除已上传的文件')
}

// 获取资源池列表
const fetchResourcePools = async () => {
  try {
    const response = await getResourcePools()
    resourcePools.value = response.data
  } catch (error) {
    console.error('获取资源池列表失败:', error)
  }
}

// 获取资源机列表
const fetchResourceMachinesList = async () => {
  try {
    const response = await getResourceMachines()
    resourceMachines.value = response.data
  } catch (error) {
    console.error('获取资源机列表失败:', error)
  }
}

// 获取RPA账号凭证列表
const fetchRpaCredentials = async () => {
  try {
    const response = await getRpaCredentials()
    rpaCredentials.value = response.data
  } catch (error) {
    console.error('获取RPA账号凭证列表失败:', error)
  }
}

// 初始化数据
const initializeData = async () => {
  await Promise.all([
    fetchResourcePools(),
    fetchResourceMachinesList(),
    fetchRpaCredentials()
  ])
}

// 添加表单验证方法
const validateForm = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 暴露验证方法供父组件调用
defineExpose({
  validateForm
})

// 在组件挂载时初始化数据
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.parameter-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

.parameter-name {
  width: 120px;
  font-weight: bold;
  margin-right: 10px;
}

.parameter-value {
  flex: 1;
  margin-right: 10px;
}

.parameter-description {
  width: 200px;
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保上传按钮和选择框不会换行 */
:deep(.el-upload) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

/* 添加新的样式 */
.upload-compact {
  display: flex;
  align-items: center;
}

.upload-compact :deep(.el-upload-list) {
  margin: 0;
  padding-left: 10px;
}

.upload-compact :deep(.el-upload-list__item) {
  margin: 0;
  line-height: 32px;
}

.uploaded-file {
  margin: 0;
  padding-left: 10px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 调整图标样式 */
.el-icon {
  margin-left: 5px;
  color: #909399;
  cursor: help;
  font-size: 16px;
}

/* 确保上传组件在一行内显示 */
:deep(.el-upload-list--text) {
  display: inline-block;
  margin-left: 10px;
}

:deep(.el-upload-list__item) {
  display: inline-block;
  margin-right: 0;
}

/* 添加必填标记的样式 */
.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}
</style>
