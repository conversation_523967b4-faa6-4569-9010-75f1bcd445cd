using Microsoft.AspNetCore.Mvc;
using RPASystem.Model;
using RPASystem.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SystemConfigController : ControllerBase
    {
        private readonly ISystemConfigService configService;

        public SystemConfigController(ISystemConfigService configService)
        {
            this.configService = configService;
        }

        /// <summary>
        /// 获取所有配置
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<SystemConfig>>> GetAll()
        {
            var configs = await configService.GetAllConfigsAsync();
            return Ok(configs);
        }

        /// <summary>
        /// 按分组获取配置
        /// </summary>
        [HttpGet("group/{group}")]
        public async Task<ActionResult<List<SystemConfig>>> GetByGroup(string group)
        {
            var configs = await configService.GetConfigsByGroupAsync(group);
            return Ok(configs);
        }

        /// <summary>
        /// 根据ID获取配置
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<SystemConfig>> GetById(long id)
        {
            var config = await configService.GetConfigByIdAsync(id);
            if (config == null) return NotFound();
            return Ok(config);
        }

        /// <summary>
        /// 根据键名获取配置
        /// </summary>
        [HttpGet("key/{key}")]
        public async Task<ActionResult<SystemConfig>> GetByKey(string key)
        {
            var config = await configService.GetConfigByKeyAsync(key);
            if (config == null) return NotFound();
            return Ok(config);
        }

        /// <summary>
        /// 根据键名获取配置值
        /// </summary>
        [HttpGet("value/{key}")]
        public async Task<ActionResult<string>> GetValue(string key, [FromQuery] string defaultValue = "")
        {
            var value = await configService.GetConfigValueAsync(key, defaultValue);
            return Ok(value);
        }

        /// <summary>
        /// 创建配置
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<bool>> Create([FromBody] SystemConfig config)
        {
            try
            {
                var result = await configService.CreateConfigAsync(config);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        [HttpPut]
        public async Task<ActionResult<bool>> Update([FromBody] SystemConfig config)
        {
            try
            {
                var result = await configService.UpdateConfigAsync(config);
                if (!result) return NotFound();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> Delete(long id)
        {
            try
            {
                var result = await configService.DeleteConfigAsync(id);
                if (!result) return NotFound();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        [HttpPut("batch")]
        public async Task<ActionResult<bool>> BatchUpdate([FromBody] BatchUpdateConfigDto dto)
        {
            try
            {
                var result = await configService.BatchUpdateConfigsAsync(dto.Configs);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        [HttpPost("initialize")]
        public async Task<ActionResult<int>> Initialize()
        {
            try
            {
                var count = await configService.InitializeDefaultConfigsAsync();
                return Ok(count);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }
    }
} 