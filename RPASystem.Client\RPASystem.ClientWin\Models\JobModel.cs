﻿using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace RPASystem.Model
{
    public class JobModel
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 任务名称
        /// </summary>
        public string JobTaskName { get; set; }
        /// <summary>
        /// 顶层任务名称
        /// </summary>
        public string TopJobTaskName { get; set; }
        /// <summary>
        /// 程序名称
        /// </summary>
        public string ProgramName { get; set; }
        /// <summary>
        /// 程序类型
        /// </summary>
        public ProgramTypeEnum ProgramType { get; set; }
        /// <summary>
        /// 运行Zip包
        /// </summary>
        //public byte[] ZipFile { get; set; }
        /// <summary>
        /// 运行参数JSON
        /// </summary>
        public string Parameter { get; set; }
        /// <summary>
        /// 程序版本
        /// </summary>
        public string ProgramVersion { get; set; }
        /// <summary>
        /// 是否独占资源机
        /// </summary>
        public bool IsExclusive { get; set; }
        /// <summary>
        /// 输出文件服务器
        /// </summary>
        public string OutputFile { get; set; }

        /// <summary>
        /// 服务器IP地址
        /// </summary>
        public string UploadServerIP
        {
            get
            {
                string ipAddress = string.Empty;
                if (!string.IsNullOrEmpty(OutputFile))
                {
                    var ipMatch = Regex.Match(OutputFile, @"(?:\\){2}([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})|^([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})");
                    ipAddress = ipMatch.Groups[1].Success ? ipMatch.Groups[1].Value : ipMatch.Groups[2].Value;
                }
                return ipAddress;
            }
        }
    }

    public enum ProgramTypeEnum
    {
        RPA,
        EXE
    }

    public enum JobTaskStatusEnum
    {
        Pending,
        Running,
        Success,
        Failed,
        Cancelled
    }
}
