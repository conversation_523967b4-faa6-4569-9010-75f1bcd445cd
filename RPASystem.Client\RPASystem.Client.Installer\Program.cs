﻿using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Linq;
using Microsoft.Win32;

namespace RPASystem.Client.Installer
{
    internal static class Program
    {
        private static readonly string BaseInstallPath = @"C:\Program Files\RPASystem.ClientWin";
        private static string defaultServerIP = "*************";

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static async Task Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                await MainAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"安装过程中发生错误：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task MainAsync()
        {
            // 1. 检测已安装程序，找版本最大的且有EXE存在的
            string existingPath = CheckExistingInstallation();
            if (!string.IsNullOrEmpty(existingPath))
            {
                // 清理其他版本目录
                CleanupOtherVersions(existingPath);
                StartClientProgram(existingPath);
                return;
            }

            // 2. 下载安装包
            byte[] zipData = await DownloadInstallPackage();
            if (zipData == null)
            {
                return; // 用户取消或下载失败
            }

            // 3. 安装部署并获取安装路径
            string installPath = InstallProgram(zipData);

            // 4. 启动程序
            StartClientProgram(installPath);
        }

        /// <summary>
        /// 检测已安装的程序，找版本最大的且有EXE存在的
        /// </summary>
        private static string CheckExistingInstallation()
        {
            if (!Directory.Exists(BaseInstallPath))
            {
                return null;
            }

            // 获取所有版本目录
            var versionDirs = Directory.GetDirectories(BaseInstallPath)
                .Select(dir => new DirectoryInfo(dir).Name)
                .Where(name => IsValidVersion(name))
                .OrderByDescending(version => new Version(version))
                .ToList();

            // 找到版本最大的且有EXE存在的
            foreach (string version in versionDirs)
            {
                string exePath = Path.Combine(BaseInstallPath, version, "RPASystem.ClientWin.exe");
                if (File.Exists(exePath))
                {
                    return exePath;
                }
            }

            return null;
        }

        /// <summary>
        /// 验证版本号格式是否有效
        /// </summary>
        private static bool IsValidVersion(string version)
        {
            try
            {
                new Version(version);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 清理其他版本目录，只保留当前版本
        /// </summary>
        private static void CleanupOtherVersions(string currentExePath)
        {
            try
            {
                if (!Directory.Exists(BaseInstallPath))
                    return;

                // 获取当前版本目录
                string currentVersionDir = Path.GetDirectoryName(currentExePath);
                string currentVersion = Path.GetFileName(currentVersionDir);

                // 获取所有版本目录
                var allVersionDirs = Directory.GetDirectories(BaseInstallPath);

                foreach (string versionDir in allVersionDirs)
                {
                    string versionName = Path.GetFileName(versionDir);
                    // 删除除当前版本外的所有目录
                    if (versionName != currentVersion)
                    {
                        try
                        {
                            Directory.Delete(versionDir, true);
                        }
                        catch
                        {
                            // 忽略删除失败的情况
                        }
                    }
                }
            }
            catch
            {
                // 忽略清理过程中的错误
            }
        }

        /// <summary>
        /// 下载安装包
        /// </summary>
        private static async Task<byte[]> DownloadInstallPackage()
        {
            // 1. 先尝试默认服务器IP
            byte[] result = await TryDownloadFromServer(defaultServerIP);
            if (result != null)
            {
                return result;
            }

            // 2. 尝试从注册表获取服务器IP
            string registryServerIP = GetServerIPFromRegistry();
            if (!string.IsNullOrEmpty(registryServerIP) && registryServerIP != defaultServerIP)
            {
                result = await TryDownloadFromServer(registryServerIP);
                if (result != null)
                {
                    return result;
                }
            }

            // 3. 如果都失败，弹出IP输入对话框让用户手动输入
            string serverIP = registryServerIP ?? defaultServerIP;
            while (true)
            {
                string newIP = ShowIPInputDialog(serverIP);
                if (string.IsNullOrEmpty(newIP))
                {
                    return null; // 用户取消
                }

                result = await TryDownloadFromServer(newIP);
                if (result != null)
                {
                    return result;
                }

                serverIP = newIP;
            }
        }

        /// <summary>
        /// 尝试从指定服务器下载安装包
        /// </summary>
        private static async Task<byte[]> TryDownloadFromServer(string serverIP)
        {
            try
            {
                string downloadUrl = $"http://{serverIP}:8888/api/update/download";

                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    var response = await httpClient.GetAsync(downloadUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        return await response.Content.ReadAsByteArrayAsync();
                    }
                }
            }
            catch (Exception)
            {
                // 下载失败，返回null
            }

            return null;
        }

        /// <summary>
        /// 从注册表获取服务器IP
        /// </summary>
        private static string GetServerIPFromRegistry()
        {
            try
            {
                using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                {
                    using (var key = baseKey.OpenSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V"))
                    {
                        if (key != null)
                        {
                            object value = key.GetValue("ServerIP");
                            if (value != null)
                            {
                                return value.ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 读取注册表失败，忽略错误
            }

            return null;
        }

        /// <summary>
        /// 显示IP输入对话框
        /// </summary>
        private static string ShowIPInputDialog(string currentIP)
        {
            Form inputForm = new Form()
            {
                Width = 350,
                Height = 150,
                Text = "RPA大助手 - 服务器设置",
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label label = new Label()
            {
                Left = 20,
                Top = 20,
                Width = 280,
                Text = $"无法连接到服务器 {currentIP}，请输入新的服务器IP地址："
            };

            TextBox textBox = new TextBox()
            {
                Left = 20,
                Top = 50,
                Width = 200,
                Text = currentIP
            };

            Button okButton = new Button()
            {
                Text = "确定",
                Left = 230,
                Top = 48,
                Width = 80,
                DialogResult = DialogResult.OK
            };

            Button cancelButton = new Button()
            {
                Text = "取消",
                Left = 230,
                Top = 78,
                Width = 80,
                DialogResult = DialogResult.Cancel
            };

            inputForm.Controls.Add(label);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(okButton);
            inputForm.Controls.Add(cancelButton);

            inputForm.AcceptButton = okButton;
            inputForm.CancelButton = cancelButton;

            if (inputForm.ShowDialog() == DialogResult.OK)
            {
                return textBox.Text.Trim();
            }

            return null;
        }



        /// <summary>
        /// 获取安装路径
        /// </summary>
        private static string GetInstallPath(string version)
        {
            return Path.Combine(BaseInstallPath, version, "RPASystem.ClientWin.exe");
        }

        /// <summary>
        /// 安装程序并返回安装路径
        /// </summary>
        private static string InstallProgram(byte[] zipData)
        {
            string tempZipPath = null;
            string version = "*******"; // 默认版本号

            try
            {
                // 创建临时zip文件
                tempZipPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".zip");
                File.WriteAllBytes(tempZipPath, zipData);

                // 先获取版本号
                using (var archive = ZipFile.OpenRead(tempZipPath))
                {
                    var versionEntry = archive.Entries.FirstOrDefault(e =>
                        e.Name.Equals("Version.dat", StringComparison.OrdinalIgnoreCase));

                    if (versionEntry != null)
                    {
                        using (var reader = new StreamReader(versionEntry.Open()))
                        {
                            version = reader.ReadToEnd().Trim();
                        }
                    }
                }

                // 确定安装目录
                string installPath = GetInstallPath(version);
                string installDir = Path.GetDirectoryName(installPath);

                // 确保安装目录存在
                Directory.CreateDirectory(installDir);

                // 如果目标目录已存在文件，先清空
                if (Directory.Exists(installDir))
                {
                    foreach (string file in Directory.GetFiles(installDir, "*", SearchOption.AllDirectories))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // 忽略删除失败的文件
                        }
                    }
                }

                // 解压到安装目录
                ZipFile.ExtractToDirectory(tempZipPath, installDir);

                return installPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"安装失败：{ex.Message}");
            }
            finally
            {
                // 确保临时文件被删除
                if (!string.IsNullOrEmpty(tempZipPath) && File.Exists(tempZipPath))
                {
                    try
                    {
                        File.Delete(tempZipPath);
                    }
                    catch
                    {
                        // 忽略删除失败的情况
                    }
                }
            }
        }

        /// <summary>
        /// 启动客户端程序
        /// </summary>
        private static void StartClientProgram(string exePath)
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    //Arguments = "-hide", // 隐藏启动
                    UseShellExecute = false,
                    WorkingDirectory = Path.GetDirectoryName(exePath)
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动程序失败：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
