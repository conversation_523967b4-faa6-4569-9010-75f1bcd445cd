<template>
  <div class="file-management">
    <!-- 上传区域 -->
    <div class="upload-area">
      <el-upload 
        class="upload-demo" 
        :http-request="customUpload" 
        :on-success="handleUploadSuccess" 
        :on-error="handleUploadError"
        style="margin-right: 20px;"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip" style="display: inline-block; margin-left: 10px;">上传文件建议不要超过30MB</div>
        </template>
      </el-upload>
      <el-input 
        v-model="uploadRemark" 
        placeholder="上传文件备注（可选）" 
        style="width: 300px;"
      />
    </div>

    <el-divider />

    <!-- 搜索和批量操作区域 -->
    <div class="operation-area">
      <div class="search-area">
        <el-input v-model="queryParams.fileName" placeholder="搜索文件名" style="width: 200px; margin-right: 10px" />
        <el-input v-model="queryParams.fileExtension" placeholder="搜索扩展名" style="width: 200px; margin-right: 10px" />
        <el-button type="primary" @click="handleQuery">搜索</el-button>
      </div>
      <div class="batch-operation">
        <el-button 
          type="danger" 
          :disabled="multiple"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 文件列表表格 -->
    <el-table 
      v-loading="loading"
      :data="fileList" 
      style="width: 100%; margin-top: 20px;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="fileName" label="文件名" width="180" show-overflow-tooltip />
      <el-table-column prop="fileExtension" label="扩展名" width="100" />
      <el-table-column prop="uploadTime" label="上传时间" width="180" />
      <el-table-column prop="modifyTime" label="修改时间" width="180" />
      <el-table-column prop="remark" label="备注" min-width="200">
        <template #default="scope">
          <el-input v-model="scope.row.remark" @blur="updateRemark(scope.row)"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleDownload(scope.row)">下载</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination 
      v-model:total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="filemanagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { listFiles, uploadFile, downloadFile, updateFileRemark, deleteFile, batchDeleteFiles } from '@/api/RPA/FileManagement'

// 遮罩层
const loading = ref(false)
// 文件列表数据
const fileList = ref([])
// 上传文件备注
const uploadRemark = ref('')
// 总条数
const total = ref(0)
// 选中数组
const ids = ref([])
// 非多个禁用
const multiple = ref(true)
// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  fileName: '',
  fileExtension: ''
})

/** 查询文件列表 */
function getList() {
  loading.value = true
  listFiles({
    pageIndex: queryParams.pageIndex,
    pageSize: queryParams.pageSize,
    searchFileName: queryParams.fileName,
    searchFileExtension: queryParams.fileExtension
  }).then(response => {
    fileList.value = response.data.items
    total.value = response.data.total
    loading.value = false
  }).catch(error => {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败,请重试')
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageIndex = 1
  getList()
}

/** 自定义上传文件 */
function customUpload(options) {
  const formData = new FormData()
  formData.append('file', options.file)
  formData.append('remark', uploadRemark.value)
  uploadFile(formData).then(response => {
    options.onSuccess(response.data)
  }).catch(error => {
    options.onError(error)
  })
}

/** 上传成功回调 */
function handleUploadSuccess() {
  getList()
  uploadRemark.value = ''
  ElMessage.success('上传成功')
}

/** 上传失败回调 */
function handleUploadError() {
  ElMessage.error('文件上传失败,请重试')
}

/** 下载文件操作 */
function handleDownload(row) {
  downloadFile(row.id).then(response => {
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${row.fileName}${row.fileExtension}`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }).catch(error => {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败,请重试')
  })
}

/** 更新文件备注 */
function updateRemark(file) {
  updateFileRemark(file.id, JSON.stringify(file.remark)).then(() => {
    getList()
  }).catch(error => {
    console.error('更新备注失败:', error)
    ElMessage.error('更新备注失败,请重试')
  })
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}

/** 删除按钮操作 */
function handleDelete(row) {
  ElMessageBox.confirm(
    '确定要删除这个文件吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    deleteFile(row.id).then(() => {
      getList()
      ElMessage.success('删除成功')
    }).catch(error => {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败，请重试')
    })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/** 批量删除按钮操作 */
function handleBatchDelete() {
  if (!ids.value.length) return
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${ids.value.length} 个文件吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    batchDeleteFiles(ids.value).then(() => {
      getList()
      ElMessage.success('批量删除成功')
    }).catch(error => {
      console.error('批量删除文件失败:', error)
      ElMessage.error('批量删除文件失败，请重试')
    })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 初始挂载时获取列表
onMounted(() => {
  getList()
})
</script>

<style scoped>
.file-management {
  padding: 20px;
}
.upload-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.search-area {
  margin: 20px 0;
  display: flex;
  align-items: center;
}
.operation-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}
.batch-operation {
  margin-left: 20px;
}
</style>
