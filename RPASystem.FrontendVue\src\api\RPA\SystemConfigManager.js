import axios from 'axios'

// 获取所有配置
export function listAllConfigs() {
  return axios.get('/api/systemconfig')
}

// 按分组获取配置
export function getConfigsByGroup(group) {
  return axios.get(`/api/systemconfig/group/${group}`)
}

// 根据ID获取配置
export function getConfigById(id) {
  return axios.get(`/api/systemconfig/${id}`)
}

// 根据键名获取配置
export function getConfigByKey(key) {
  return axios.get(`/api/systemconfig/key/${key}`)
}

// 根据键名获取配置值
export function getConfigValue(key, defaultValue = '') {
  return axios.get(`/api/systemconfig/value/${key}`, {
    params: { defaultValue }
  })
}

// 创建配置
export function createConfig(data) {
  return axios.post('/api/systemconfig', data)
}

// 更新配置
export function updateConfig(data) {
  return axios.put('/api/systemconfig', data)
}

// 删除配置
export function deleteConfig(id) {
  return axios.delete(`/api/systemconfig/${id}`)
}

// 批量更新配置
export function batchUpdateConfigs(data) {
  return axios.put('/api/systemconfig/batch', data)
}

// 初始化默认配置
export function initializeConfigs() {
  return axios.post('/api/systemconfig/initialize')
} 