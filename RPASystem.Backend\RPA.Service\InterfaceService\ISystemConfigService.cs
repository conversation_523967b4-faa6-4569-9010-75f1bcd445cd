using RPASystem.Model;

namespace RPASystem.Service
{
    public interface ISystemConfigService
    {
        Task<List<SystemConfig>> GetAllConfigsAsync();
        Task<List<SystemConfig>> GetConfigsByGroupAsync(string group);
        Task<SystemConfig> GetConfigByIdAsync(long id);
        Task<SystemConfig> GetConfigByKeyAsync(string key);
        Task<string> GetConfigValueAsync(string key, string defaultValue = "");
        Task<bool> CreateConfigAsync(SystemConfig config);
        Task<bool> UpdateConfigAsync(SystemConfig config);
        Task<bool> DeleteConfigAsync(long id);
        Task<bool> BatchUpdateConfigsAsync(List<SystemConfigDto> configs);
        Task<int> InitializeDefaultConfigsAsync();
    }
} 