﻿using Microsoft.Win32;
using RPASystem.Client.Common;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RPASystem.Client
{
    internal static class MachineMan
    {
        static MachineMan()
        {
            InitConfig(ref serverIP, "ServerIP", "*************");
            //InitConfig(ref serverPort, "ServerPort", "8888");
            //InitConfig(ref frontendPort, "FrontendPort", "8887");
        }

        private static void InitConfig(ref string field, string key, string defaultValue)
        {
            field = EnvParUtils.Read(key);
            if (string.IsNullOrEmpty(field))
            {
                field = defaultValue;
                EnvParUtils.Write(key, defaultValue);
            }
        }

        private static void SetConfig(ref string field, string key, string value)
        {
            field = value;
            EnvParUtils.Write(key, value);
        }

        public static string BaseUrl => $"http://{ServerIP}:{ServerPort}";
        public static string BaseUrlFrontend => $"http://{ServerIP}:{FrontendPort}";
        public static string MainExeName => "RPASystem.Client";

        static string serverIP;
        public static string ServerIP
        {
            get => serverIP;
            set => SetConfig(ref serverIP, "ServerIP", value);
        }

        //static string serverPort;
        //public static string ServerPort
        //{
        //    get => serverPort;
        //    set => SetConfig(ref serverPort, "ServerPort", value);
        //}
        public static string ServerPort => "8888";

        //static string frontendPort;
        //public static string FrontendPort
        //{
        //    get => frontendPort;
        //    set => SetConfig(ref frontendPort, "FrontendPort", value);
        //}


        public static string FrontendPort => "8887";

        //const string RegisterKeyName = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";  // 这个不启动
        const string RegisterKeyName = @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run";
        public static bool AutoRun
        {
            get => Registry.GetValue("HKEY_LOCAL_MACHINE\\" + RegisterKeyName, MainExeName, null)?.ToString() != null;
            set { SetAutoStart(value); }
        }

        static void SetAutoStart(bool isAutoStart = true)
        {
            var exePath = Process.GetCurrentProcess().MainModule.FileName;
            if (isAutoStart)
                Registry.SetValue("HKEY_LOCAL_MACHINE\\" + RegisterKeyName, MainExeName, $"{exePath} -hide");
            else
                try { Registry.LocalMachine.OpenSubKey(RegisterKeyName, true)?.DeleteValue(MainExeName, false); } catch { }
        }

        public static string Version { get; set; }

        private static void UpdateAppSetting(string key, string value)
        {
            var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            var settings = config.AppSettings.Settings;
            if (settings[key] != null)
                settings[key].Value = value;
            else
                settings.Add(key, value);
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
        }

        public static string GetHostname()
        {
            try
            {
                //using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                //{
                //    using (var key = baseKey.OpenSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V"))
                //    {
                //        if (key != null)
                //        {
                //            var value = key.GetValue("WAssName");
                //            if (value != null)
                //            {
                //                return value.ToString();
                //            }
                //        }
                //    }
                //}
                return EnvParUtils.Read("WAssName", "V");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 获取主机名时发生错误: {ex.Message}");
            }
            return Environment.MachineName;
        }

        public static bool SetHostname(string name)
        {
            try
            {
                //using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                //{
                //    using (var key = baseKey.CreateSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V", true))
                //    {
                //        if (key != null)
                //        {
                //            key.SetValue("WAssName", name, RegistryValueKind.String);
                //            return true;
                //        }
                //    }
                //}
                EnvParUtils.Write("WAssName", name);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 设置主机名时发生错误: {ex.Message}");
            }
            return false;
        }
    }
}
