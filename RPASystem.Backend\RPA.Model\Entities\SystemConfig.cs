using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RPASystem.Model
{
    /// <summary>
    /// 系统配置表
    /// </summary>
    [Table("SystemConfig")]
    public class SystemConfig
    {
        [Key]
        public long ID { get; set; }

        /// <summary>
        /// 配置键名
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string ConfigKey { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string ConfigValue { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        [MaxLength(50)]
        public string ConfigGroup { get; set; }

        /// <summary>
        /// 是否系统内置（内置配置不可删除）
        /// </summary>
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }
} 