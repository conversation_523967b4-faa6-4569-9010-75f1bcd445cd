using Newtonsoft.Json;
using RPASystem.Model;
using System.Collections.Generic;

public class RunResult
{
    public bool IsSucceed { get; set; }
    public string ReturnResult { get; set; }
    public List<string> ErrorMessage { get; set; }
    public int? Status { get; set; } // 0: 异常失败, 1: 成功, 2: 警告 ,3:取消
    public JobTaskStatusEnum JobTaskStatus
    {
        get
        {
            if (Status != null && Status == 3)
            {
                return JobTaskStatusEnum.Cancelled;
            }
            else
            {
                return IsSucceed ? JobTaskStatusEnum.Success : JobTaskStatusEnum.Failed;
            }
            //switch (Status)
            //{
            //    case 1: return JobTaskStatusEnum.Success;
            //    case 3: return JobTaskStatusEnum.Cancelled;
            //    default:
            //        return JobTaskStatusEnum.Failed;
            //}
        }
    } // 任务状态
    // 本地输出文件路径
    public string OutputFileLocalPath { get; set; }

    public string ToJson()
    {
        return JsonConvert.SerializeObject(this, new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore
        });
    }

    public static RunResult FromJson(string json)
    {
        return JsonConvert.DeserializeObject<RunResult>(json);
    }

    public string GetErrorMessage()
    {
        if (ErrorMessage == null || ErrorMessage.Count == 0)
            return string.Empty;
        return string.Join("\n", ErrorMessage);
    }
}

public class RunResultOld
{
    public bool IsSucceed { get; set; }
    public string ReturnResult { get; set; }
    public List<string> ErrorMessage { get; set; }
    public int? Status { get; set; } // 0: 异常失败, 1: 成功, 2: 警告

    public string ToJson()
    {
        return JsonConvert.SerializeObject(this, new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore
        });
    }

    public static RunResult FromJson(string json)
    {
        return JsonConvert.DeserializeObject<RunResult>(json);
    }
}