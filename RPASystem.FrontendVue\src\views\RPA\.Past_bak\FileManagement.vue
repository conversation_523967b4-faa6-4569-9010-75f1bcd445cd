<template>
  <div class="file-management">
    <!-- 上传区域 -->
    <div class="upload-area">
      <el-upload 
        class="upload-demo" 
        :http-request="customUpload" 
        :on-success="handleUploadSuccess" 
        :on-error="handleUploadError"
        style="margin-right: 20px;"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip" style="display: inline-block; margin-left: 10px;">上传文件建议不要超过30MB</div>
        </template>
      </el-upload>
      <el-input 
        v-model="uploadRemark" 
        placeholder="上传文件备注（可选）" 
        style="width: 300px;"
      />
    </div>

    <el-divider />

    <!-- 搜索和批量操作区域 -->
    <div class="operation-area">
      <div class="search-area">
        <el-input v-model="searchForm.fileName" placeholder="搜索文件名" style="width: 200px; margin-right: 10px" />
        <el-input v-model="searchForm.fileExtension" placeholder="搜索扩展名" style="width: 200px; margin-right: 10px" />
        <el-button type="primary" @click="loadFiles">搜索</el-button>
      </div>
      <div class="batch-operation">
        <el-button 
          type="danger" 
          :disabled="!selectedFiles.length"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 修改表格，添加多选列和删除按钮 -->
    <el-table 
      :data="files" 
      style="width: 100%; margin-top: 20px;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="fileName" label="文件名" width="180" show-overflow-tooltip />
      <el-table-column prop="fileExtension" label="扩展名" width="100" />
      <el-table-column prop="uploadTime" label="上传时间" width="180" />
      <el-table-column prop="modifyTime" label="修改时间" width="180" />
      <el-table-column prop="remark" label="备注" min-width="200">
        <template #default="scope">
          <el-input v-model="scope.row.remark" @blur="updateRemark(scope.row)"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="downloadFile(scope.row.id, scope.row.fileName, scope.row.fileExtension)">下载</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessageBox, ElMessage } from 'element-plus'

export default {
  name: 'FileManagement',
  setup() {
    const files = ref([])
    const uploadRemark = ref('')
    const pageIndex = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const searchForm = ref({
      fileName: '',
      fileExtension: ''
    })

    const selectedFiles = ref([])

    const loadFiles = async () => {
      try {
        const response = await axios.get('/api/filestorage/list', {
          params: {
            pageIndex: pageIndex.value,
            pageSize: pageSize.value,
            searchFileName: searchForm.value.fileName,
            searchFileExtension: searchForm.value.fileExtension
          }
        })
        files.value = response.data.items
        total.value = response.data.total
      } catch (error) {
        console.error('加载文件列表失败:', error)
        alert('加载文件列表失败,请重试')
      }
    }

    const handleSizeChange = (val) => {
      pageSize.value = val
      loadFiles()
    }

    const handleCurrentChange = (val) => {
      pageIndex.value = val
      loadFiles()
    }

    onMounted(loadFiles)

    const customUpload = async (options) => {
      const formData = new FormData()
      formData.append('file', options.file)
      formData.append('remark', uploadRemark.value)
      try {
        const response = await axios.post('/api/filestorage/upload', formData)
        options.onSuccess(response.data)
      } catch (error) {
        options.onError(error)
      }
    }

    const handleUploadSuccess = (response) => {
      loadFiles()
      uploadRemark.value = ''
    }

    const handleUploadError = () => {
      alert('文件上传失败,请重试')
    }

    const downloadFile = async (id, fileName, fileExtension) => {
      try {
        const response = await axios.get(`/api/filestorage/${id}`, { 
          responseType: 'blob',
          headers: { 'Accept': 'application/octet-stream' }
        })
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `${fileName}${fileExtension}`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载文件失败:', error)
        alert('下载文件失败,请重试')
      }
    }

    const updateRemark = async (file) => {
      try {
        await axios.put(`/api/filestorage/${file.id}/remark`, JSON.stringify(file.remark), {
          headers: { 'Content-Type': 'application/json' }
        })
        loadFiles()
      } catch (error) {
        console.error('更新备注失败:', error)
        alert('更新备注失败,请重试')
      }
    }

    // 处理表格多选变化
    const handleSelectionChange = (selection) => {
      selectedFiles.value = selection
    }

    // 删除单个文件
    const handleDelete = (file) => {
      ElMessageBox.confirm(
        '确定要删除这个文件吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          await axios.delete(`/api/filestorage/${file.id}`)
          ElMessage.success('删除成功')
          loadFiles()
        } catch (error) {
          console.error('删除文件失败:', error)
          ElMessage.error('删除文件失败，请重试')
        }
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
    }

    // 批量删除文件
    const handleBatchDelete = () => {
      if (!selectedFiles.value.length) return

      ElMessageBox.confirm(
        `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          const fileIds = selectedFiles.value.map(file => file.id)
          await axios.post('/api/filestorage/batchdelete', fileIds)
          ElMessage.success('批量删除成功')
          loadFiles()
        } catch (error) {
          console.error('批量删除文件失败:', error)
          ElMessage.error('批量删除文件失败，请重试')
        }
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
    }

    return {
      files,
      uploadRemark,
      pageIndex,
      pageSize,
      total,
      searchForm,
      customUpload,
      handleUploadSuccess,
      handleUploadError,
      downloadFile,
      updateRemark,
      loadFiles,
      handleSizeChange,
      handleCurrentChange,
      selectedFiles,
      handleSelectionChange,
      handleDelete,
      handleBatchDelete,
    }
  }
}
</script>

<style scoped>
.file-management {
  padding: 20px;
}
.upload-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.search-area {
  margin: 20px 0;
  display: flex;
  align-items: center;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.operation-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}
.batch-operation {
  margin-left: 20px;
}
</style>
