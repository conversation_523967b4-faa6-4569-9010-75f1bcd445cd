﻿using Microsoft.Win32;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;


/// <summary>
/// 使用条件，必须安装Winrar.exe或在运行目录有Winrar.exe或\Winrar\Winrar.exe
/// </summary>
public class ZipRarUtils
{
    private const string RarExe = "WinRar\\WinRAR.exe";
    private static string winRarPath;

    /// <summary>
    /// WinRAR安装路径，可以自己设置，默认读取系统注册表和运行目录
    /// </summary>
    public static string WinRarPath
    {
        get
        {
            return string.IsNullOrEmpty(winRarPath) ? GetWinRarInstallPath() : winRarPath;
        }

        set
        {
            if (File.Exists(value))
            {
                winRarPath = value;
            }
            else
            {
                throw new ArgumentNullException("未能找到WinRAR路径！");
            }
        }
    }

    /// <summary>
    /// UnRAR安装路径
    /// </summary>
    public static string UnRarPath
    {
        get
        {
            return WinRarPath.Replace("WinRAR.exe", "UnRAR.exe");
        }
    }

    /// <summary>
    /// 获取文件后缀
    /// </summary>
    /// <param name="rarfilePaths">读取的压缩包内文件路径</param>
    /// <returns>返回压缩包内文件后缀名列表</returns>
    private static List<string> GetFileSuffix(List<string> rarfilePaths)
    {
        if (rarfilePaths == null || !rarfilePaths.Any())
        {
            return new List<string>();
        }

        var suffixs = new ConcurrentBag<string>();

        //并行处理
        Parallel.ForEach(rarfilePaths, (filePath) =>
        {
            var lastDotIndex = filePath.LastIndexOf('.');
            if (lastDotIndex == -1)
            {
                return;
            }

            var suffix = filePath.Substring(lastDotIndex + 1);
            if (!suffixs.Any(_ => string.Equals(_, suffix, StringComparison.OrdinalIgnoreCase)))
            {
                suffixs.Add(suffix);
            }
        });

        return suffixs.ToList();
    }


    /// <summary>
    /// 获取RAR压缩包里的文件信息
    /// </summary>
    /// <param name="rarFilePath">rar压缩文件的路径</param>
    /// <returns>返回压缩包内文件路径列表</returns>
    private static List<string> GetRarInternalFilePathList(string rarFilePath)
    {
        if (string.IsNullOrEmpty(rarFilePath))
        {
            throw new ArgumentNullException("文件路径不能为空");
        }

        if (!File.Exists(rarFilePath))
        {
            throw new FileNotFoundException($"压缩文件不存在，文件路径:{rarFilePath}");
        }

        var fileList = new List<string>();

        RunCmd($"lb \"{rarFilePath}\"", UnRarPath, out string results);

        if (!string.IsNullOrEmpty(results))
        {
            fileList.AddRange(results.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
        }

        return fileList;
    }

    /// <summary>
    /// 获取Zip压缩包里的文件信息
    /// </summary>
    /// <param name="zipFilePath">zip压缩文件的路径</param>
    /// <returns>返回压缩包内文件路径列表</returns>
    private static List<string> GetZipInternalFilePathList(string zipFilePath)
    {
        if (string.IsNullOrEmpty(zipFilePath))
        {
            throw new ArgumentNullException("文件路径不能为空");
        }

        if (!File.Exists(zipFilePath))
        {
            throw new FileNotFoundException($"压缩文件不存在，文件路径:{zipFilePath}");
        }

        var fileList = new List<string>();

        using (var zf = ZipFile.OpenRead(zipFilePath))
        {
            foreach (var entry in zf.Entries)
            {
                fileList.Add(entry.FullName);
            }
        }

        return fileList;
    }

    /// <summary>
    /// 获取压缩包内部文件信息
    /// </summary>
    /// <param name="filePath">压缩包文件路径</param>
    /// <returns>返回压缩包文件内部路径列表</returns>
    public static List<string> GetFilePaths(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
        {
            throw new ArgumentNullException("文件路径不能为空");
        }

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"压缩文件不存在，文件路径:{filePath}");
        }

        var lastDotIndex = filePath.LastIndexOf('.');
        var suffix = filePath.Substring(lastDotIndex + 1);
        switch (suffix)
        {
            case "zip":
                return GetZipInternalFilePathList(filePath);
            case "rar":
                return GetRarInternalFilePathList(filePath);
            default:
                throw new InvalidOperationException("不支持压缩包格式");
        }
    }


    /// <summary>
    /// 解压到某个文件夹中
    /// </summary>
    /// <param name="rarFilePath">rar文件全路径</param>
    /// <param name="rarPath">解压到哪个文件夹</param>
    /// <param name="password">是否解压完整路径</param>
    /// <param name="isFullPath">是否解压完整路径</param>
    /// <param name="isOverride">是否覆盖</param>
    /// <param name="matchList">匹配规则列表。如：*.xlsx 、*QCC*</param>
    /// <returns>是否成功</returns>
    public static bool Decompress(string rarFilePath, string rarPath, string password = "", bool isFullPath = true, bool isOverride = true, IEnumerable<string> matchList = null)
    {
        #region 匹配规则
        string matchs = "";
        if (matchList != null && matchList.Any())
        {
            matchs = string.Join(" ", matchList.Select(x => $"\"{x}\""));
        }
        #endregion

        rarPath = rarPath.EndsWith("\\") ? rarPath : rarPath + "\\";
        // -s1  静默模式，没有窗口
        return RunCmd(string.Format("{0} -s1  {1} -o{2} -y \"{3}\" \"{4}\" {5}", isFullPath ? "x" : "e", string.IsNullOrEmpty(password) ? " -p-" : " -p" + password, isOverride ? "+" : "-", rarFilePath, rarPath, matchs), WinRarPath, out string _);
    }

    /// <summary>
    /// 压缩文件或者文件夹为压缩包
    /// </summary>
    /// <param name="fileOrDirPath">需要压缩的文件/文件夹全路径</param>
    /// <param name="saveFilePath">压缩文件保存全路径</param>
    /// <param name="isOverride">是否覆盖</param>
    /// <param name="password">压缩文件密码</param>
    /// <returns>是否成功</returns>
    public static bool Compress(string fileOrDirPath, string saveFilePath, bool isOverride = true, string password = null)
    {
        return RunCmd(string.Format("a {0} -o{1}-ibck -inul -ep1 -y -r -m1 -mt6 \"{2}\" \"{3}\"", password == null ? string.Empty : " -p" + password, isOverride ? "+" : "-", saveFilePath, fileOrDirPath), WinRarPath, out string _);
    }

    /// <summary>
    /// 从注册表中获取WinRAR的安装路径或在程序目录获取WINRAR
    /// </summary>
    /// <returns>返回路径</returns>
    public static string GetWinRarInstallPath()
    {
        var openKey = @"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\WinRAR.exe";
        RegistryKey appPath = Registry.LocalMachine.OpenSubKey(openKey);
        if (appPath != null)
        {
            string path = appPath.GetValue(string.Empty).ToString();
            if (File.Exists(path))
            {
                return path;
            }
        }

        var winRarPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), RarExe);
        if (File.Exists(winRarPath))
        {
            return winRarPath;
        }

        winRarPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), RarExe);
        if (File.Exists(winRarPath))
        {
            return winRarPath;
        }

        winRarPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, RarExe);
        if (File.Exists(winRarPath))
        {
            return winRarPath;
        }

        winRarPath = AppDomain.CurrentDomain.BaseDirectory + "WinRAR.exe";
        if (File.Exists(winRarPath))
        {
            return winRarPath;
        }

        throw new ArgumentNullException("WinRAR未安装");
    }

    /// <summary>
    /// 执行命令
    /// </summary>
    /// <param name="cmd">要执行的命令</param>
    /// <param name="fileName">应用程序路劲</param>
    /// <param name="consoleStr">读取控制台内容</param>
    /// <returns>是否执行成功</returns>
    private static bool RunCmd(string cmd, string fileName, out string consoleStr)
    {
        using (var p = new Process())
        {
            p.StartInfo.FileName = fileName;
            p.StartInfo.UseShellExecute = false;
            p.StartInfo.RedirectStandardInput = true;
            p.StartInfo.RedirectStandardOutput = true;
            p.StartInfo.RedirectStandardError = true;
            p.StartInfo.CreateNoWindow = true;
            p.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
            p.StartInfo.Arguments = cmd;
            p.Start();
            consoleStr = p.StandardOutput.ReadToEnd();
            p.WaitForExit();
            if (p.ExitCode == 0)
            {
                p.Close();
                p.Dispose();
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}

