using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using RPASystem.Service;
using Infrastructure.Attribute;
using Microsoft.VisualBasic;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IRpaCredentialService), ServiceLifetime = LifeTime.Scoped)]
    public class RpaCredentialService : IRpaCredentialService
    {
        private readonly RPASystemDbContext db;

        public RpaCredentialService(RPASystemDbContext context)
        {
            this.db = context;
        }

        public async Task<List<RpaCredential>> GetAllCredentials()
        {
            return await db.RpaCredentials.OrderByDescending(c => c.CreatedTime).ToListAsync();
        }

        public async Task<RpaCredential> GetCredentialById(long id)
        {
            return await db.RpaCredentials.FindAsync(id);
        }

        public async Task<bool> CreateCredential(RpaCredential credential)
        {
            try
            {
                credential.CreatedTime = DateTime.Now;
                await db.RpaCredentials.AddAsync(credential);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> UpdateCredential(RpaCredential credential)
        {
            try
            {
                var existing = await db.RpaCredentials.FindAsync(credential.ID);
                if (existing == null) return false;

                existing.Username = credential.Username;
                existing.Password = credential.Password;
                existing.Description = credential.Description;
                existing.UpdatedTime = DateTime.Now;
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> DeleteCredential(long id)
        {
            try
            {
                var credential = await db.RpaCredentials.FindAsync(id);
                if (credential == null) return false;

                db.RpaCredentials.Remove(credential);
                await db.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 通过用户名获取凭证密文
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>密文信息</returns>
        public async Task<string> GetPasswordByUsername(string username)
        {
            try
            {
                var credential = await db.RpaCredentials
                    .Where(c => c.Username == username)
                    .Select(c => c.Password)
                    .FirstOrDefaultAsync();

                return credential ?? string.Empty;
            }
            catch
            {
                throw;
            }
        }
    }
}