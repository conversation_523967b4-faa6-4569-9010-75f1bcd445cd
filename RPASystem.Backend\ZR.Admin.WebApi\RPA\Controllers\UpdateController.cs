using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using RPASystem.WebApi.Hubs;
using System.IO;
using System.IO.Compression;
using System.Diagnostics;
using System.Xml.Linq;
using RPASystem.Service;
using RPASystem.Service.Common;
using ZR.Admin.WebApi.RPA.Common;
using System.Threading.Tasks;

namespace RPASystem.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UpdateController : ControllerBase
    {
        private readonly IHubContext<ResourceMachineHub> hubContext;
        private readonly IResourceMachineService resourceMachineService;
        //private const string ClientPackageType = "ClientPackage";
        //private const string VERSION_FILE = "version.txt";

        public UpdateController(IHubContext<ResourceMachineHub> hubContext, IResourceMachineService resourceMachineService)
        {
            this.hubContext = hubContext;
            this.resourceMachineService = resourceMachineService;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadUpdate(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("未上传文件");

                // 保存ZIP文件
                var zipPath = VersionMan.GetZipFilePath();
                using (var stream = new FileStream(zipPath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 从ZIP包中读取Version.dat文件
                string version = await GetVersionFromZip(zipPath);
                if (string.IsNullOrEmpty(version))
                    return BadRequest("无法从更新包中读取版本号");

                // 保存版本号到文件
                VersionMan.SetCurrentVersion(version);
                //var versionPath = FileUploadManager.GetFileRealPath(ClientPackageType, VERSION_FILE);
                //await System.IO.File.WriteAllTextAsync(versionPath, version);

                // 将所有资源机对比版本，如果版本不同，则更新
                await resourceMachineService.UpdateMachineVersionStatusAsync(version);
                // 通知所有在线客户端有新版本
                await hubContext.Clients.All.SendAsync("NewVersionUpdate", version);

                return Ok(new { message = "更新包上传成功", version });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"内部服务器错误: {ex.Message}");
            }
        }

        private async Task<string> GetVersionFromZip(string zipPath)
        {
            try
            {
                using (var archive = ZipFile.OpenRead(zipPath))
                {
                    // 查找Version.dat文件
                    var versionEntry = archive.Entries.FirstOrDefault(e => 
                        e.Name.Equals("Version.dat", StringComparison.OrdinalIgnoreCase));

                    if (versionEntry == null)
                        throw new FileNotFoundException("在更新包中未找到Version.dat文件");

                    // 读取版本信息
                    using (var reader = new StreamReader(versionEntry.Open()))
                    {
                        return (await reader.ReadToEndAsync()).Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取版本号失败: {ex.Message}");
            }
        }

        [HttpGet("check")]
        public async Task<IActionResult> CheckUpdate()
        {
            //var versionPath = FileUploadManager.GetFileRealPath(ClientPackageType, VERSION_FILE);
            var version = await VersionMan.GetCurrentVersion();
            var zipPath = VersionMan.GetZipFilePath();  //FileUploadManager.GetFileRealPath(ClientPackageType, "latest.zip");

            if (string.IsNullOrEmpty(version) || !System.IO.File.Exists(zipPath))
                return NotFound("未找到更新包");

            //var version = System.IO.File.ReadAllText(versionPath);
            var downloadUrl = $"{Request.Scheme}://{Request.Host}/api/update/download";
            var changelogUrl = $"{Request.Scheme}://{Request.Host}/api/update/changelog";

            
            var xmlDoc = new XDocument(
                new XElement("item",
                    new XElement("version", version),
                    new XElement("url", downloadUrl),
                    new XElement("changelog", changelogUrl),
                    new XElement("mandatory", new XAttribute("mode", "2"), "true"),
                    new XElement("args", "-hide")
                )
            );

            return Content(xmlDoc.ToString(), "application/xml");
        }

        [HttpGet("download")]
        public IActionResult DownloadUpdate()
        {
            var zipPath = VersionMan.GetZipFilePath();
            if (!System.IO.File.Exists(zipPath))
                return NotFound("更新包不存在");

            var fileStream = new FileStream(zipPath, FileMode.Open, FileAccess.Read);
            return File(fileStream, "application/zip", "latest.zip");
        }

        [HttpGet("changelog")]
        public IActionResult GetChangelog()
        {
            // 这里可以返回更新日志，现在先返回简单的文本
            return Content("系统更新", "text/plain");
        }

        [HttpGet("version")]
        public async Task<IActionResult> GetLatestVersion()
        {
            //var versionPath = FileUploadManager.GetFileRealPath(ClientPackageType, VERSION_FILE);
            //if (!System.IO.File.Exists(versionPath))
            //    return NotFound("未找到版本信息");

            var version = await VersionMan.GetCurrentVersion();
            return string.IsNullOrEmpty(version) ? NotFound("未找到版本信息") :  Ok(new { version });
        }
    }
} 