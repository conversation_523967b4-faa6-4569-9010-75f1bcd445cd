﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace RPASystem.Client
{
    public class PackagesMan
    {
        private readonly string programPath;
        private readonly string version;
        private readonly string versionFilePath;
        private string programName;

        public PackagesMan(string programName, string programPath, string version)
        {
            this.programName = programName;
            this.programPath = programPath;
            this.version = version;
            this.versionFilePath = Path.Combine(programPath, "Version.dat");
        }

        /// <summary>
        /// 检查程序包版本,不同则从服务端拉取
        /// </summary>
        public async Task CheckAndUpdateAsync()
        {
            string localVersion = GetLocalVersionAsync();
            // 版本不同或本地版本不存在,需要更新
            if (localVersion != version)
            {
                // 从服务端拉取最新程序包
                await PullLatestPackageAsync();

                // 更新版本号文件
                File.WriteAllText(versionFilePath, version);
            }
        }

        /// <summary>
        /// 获取本地版本号
        /// </summary>
        private string GetLocalVersionAsync()
        {
            if (!File.Exists(versionFilePath))
            {
                return string.Empty;
            }

            return File.ReadAllText(versionFilePath).Trim();
        }

        /// <summary>
        /// 从服务端拉取最新程序包
        /// </summary>
        private async Task PullLatestPackageAsync()
        {
            using (var httpClient = new HttpClient())
            {
                // 设置较长的超时时间，适应大文件下载
                httpClient.Timeout = TimeSpan.FromMinutes(30);

                // 构建请求URL
                string requestUrl = $"{MachineMan.BaseUrl}/api/ExeProgram/download/{programName}";

                try
                {
                    // 确保上级目录存在，否则保存zip文件会报错
                    try { Directory.CreateDirectory(Path.GetDirectoryName(programPath)); } catch { }

                    // 创建临时文件用于保存下载的ZIP
                    string tempZipFile = programPath + ".zip";

                    // 使用流式下载，避免一次性加载整个文件到内存
                    using (var response = await httpClient.GetAsync(requestUrl, HttpCompletionOption.ResponseHeadersRead))
                    {
                        // 检查响应状态
                        if (!response.IsSuccessStatusCode)
                        {
                            throw new Exception($"从服务器获取程序包失败: {response.StatusCode}");
                        }

                        // 将响应内容直接写入临时文件
                        using (var contentStream = await response.Content.ReadAsStreamAsync())
                        using (var fileStream = new FileStream(tempZipFile, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true))
                        {
                            await contentStream.CopyToAsync(fileStream);
                            await fileStream.FlushAsync();
                        }
                    }

                    // 如果目标目录存在则先删除
                    if (Directory.Exists(programPath))
                    {
                        RetryH.Execute(() => Directory.Delete(programPath, true));
                    }

                    // 确保目标目录存在
                    RetryH.Execute(() => Directory.CreateDirectory(programPath));

                    // 从临时文件解压
                    try
                    {
                        ZipFile.ExtractToDirectory(tempZipFile, programPath);
                    }
                    catch
                    {
                        // 解压失败尝试外部工具
                        ZipRarUtils.Decompress(tempZipFile, programPath);
                    }

                    // 清理临时文件
                    RetryH.Execute(() =>
                    {
                        if (File.Exists(tempZipFile))
                        {
                            File.Delete(tempZipFile);
                        }
                    });
                }
                catch (Exception ex)
                {
                    throw new Exception($"处理程序包失败: {ex.Message}", ex);
                }
            }
        }
    }
}

